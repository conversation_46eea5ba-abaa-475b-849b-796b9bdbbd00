import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../app/themes/theme_provider.dart';
import '../../../../core/constants/dimensions.dart';
import '../../../../shared/utils/responsive_utils.dart';

/// 响应式导航组件
class ResponsiveNavigation extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onDestinationSelected;
  
  const ResponsiveNavigation({
    super.key,
    required this.selectedIndex,
    required this.onDestinationSelected,
  });

  @override
  Widget build(BuildContext context) {
    // 小屏幕 (< 600): 底部导航栏
    if (ResponsiveUtils.isMobile(context)) {
      return _buildBottomNavigation(context);
    } 
    // 中等屏幕 (600-900): 仅图标的侧边导航栏
    else if (ResponsiveUtils.isTablet(context)) {
      return _buildCompactSideNavigation(context);
    } 
    // 大屏幕 (>= 900): 图标在左文字在右的导航栏
    else {
      return _buildExtendedSideNavigation(context);
    }
  }

  // 底部导航栏样式（移动端）
  Widget _buildBottomNavigation(BuildContext context) {
    final theme = Theme.of(context);
    
    return BottomNavigationBar(
      items: const <BottomNavigationBarItem>[
        BottomNavigationBarItem(
          icon: Icon(Icons.chat_bubble_outline),
          activeIcon: Icon(Icons.chat_bubble),
          label: 'AI对话',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.grid_view_outlined),
          activeIcon: Icon(Icons.grid_view_sharp),
          label: '排盘',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person_outline),
          activeIcon: Icon(Icons.person),
          label: '我的',
        ),
      ],
      currentIndex: selectedIndex,
      onTap: onDestinationSelected,
      selectedItemColor: theme.colorScheme.primary,
      unselectedItemColor: theme.brightness == Brightness.dark 
        ? Colors.grey.shade400 
        : Colors.grey.shade600,
      backgroundColor: theme.colorScheme.surface,
      elevation: 8,
      type: BottomNavigationBarType.fixed,
    );
  }

  // 紧凑侧边导航栏（中等屏幕 - 仅图标）
  Widget _buildCompactSideNavigation(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      width: AppDimensions.compactNavWidth,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: isDark ? Border(
          right: BorderSide(
            color: Colors.grey.shade800,
            width: 0.5,
          ),
        ) : null,
        boxShadow: isDark ? null : [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(1, 0),
            blurRadius: 3,
          ),
        ],
      ),
      child: Column(
        children: [
          const SizedBox(height: 24),
          _buildIconOnlyNavItem(
            context: context,
            index: 0,
            icon: Icons.chat_bubble_outline,
            activeIcon: Icons.chat_bubble,
            tooltip: 'AI对话',
          ),
          _buildIconOnlyNavItem(
            context: context,
            index: 1,
            icon: Icons.grid_view_outlined,
            activeIcon: Icons.grid_view_sharp,
            tooltip: '排盘',
          ),
          _buildIconOnlyNavItem(
            context: context,
            index: 2,
            icon: Icons.person_outline,
            activeIcon: Icons.person,
            tooltip: '我的',
          ),
          // 主题切换按钮（底部）
          const Expanded(child: SizedBox()),
          _buildThemeToggleButton(context, isCompact: true),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  // 扩展侧边导航栏（大屏幕）
  Widget _buildExtendedSideNavigation(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      width: AppDimensions.standardNavWidth,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: isDark ? Border(
          right: BorderSide(
            color: Colors.grey.shade800,
            width: 0.5,
          ),
        ) : null,
        boxShadow: isDark ? null : [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(1, 0),
            blurRadius: 3,
          ),
        ],
      ),
      child: Column(
        children: [
          const SizedBox(height: 24),
          _buildExtendedNavItem(
            context: context,
            index: 0,
            icon: Icons.chat_bubble_outline,
            activeIcon: Icons.chat_bubble,
            label: 'AI对话',
          ),
          _buildExtendedNavItem(
            context: context,
            index: 1,
            icon: Icons.grid_view_outlined,
            activeIcon: Icons.grid_view_sharp,
            label: '排盘',
          ),
          _buildExtendedNavItem(
            context: context,
            index: 2,
            icon: Icons.person_outline,
            activeIcon: Icons.person,
            label: '我的',
          ),
          // 主题切换按钮（底部）
          const Expanded(child: SizedBox()),
          _buildThemeToggleButton(context, isCompact: false),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  // 仅图标导航项（中等屏幕）
  Widget _buildIconOnlyNavItem({
    required BuildContext context,
    required int index,
    required IconData icon,
    required IconData activeIcon,
    required String tooltip,
  }) {
    final theme = Theme.of(context);
    final isSelected = selectedIndex == index;
    final isDark = theme.brightness == Brightness.dark;
    
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: () => onDestinationSelected(index),
        child: Container(
          width: AppDimensions.compactNavWidth,
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: 48,
            decoration: BoxDecoration(
              color: isSelected 
                ? theme.colorScheme.primary.withOpacity(0.1)
                : Colors.transparent,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              isSelected ? activeIcon : icon,
              color: isSelected 
                ? theme.colorScheme.primary 
                : (isDark ? Colors.grey.shade400 : Colors.grey.shade600),
              size: 24,
            ),
          ),
        ),
      ),
    );
  }

  // 扩展导航项（图标左侧文字右侧）
  Widget _buildExtendedNavItem({
    required BuildContext context,
    required int index,
    required IconData icon,
    required IconData activeIcon,
    required String label,
  }) {
    final theme = Theme.of(context);
    final isSelected = selectedIndex == index;
    final isDark = theme.brightness == Brightness.dark;
    
    return InkWell(
      onTap: () => onDestinationSelected(index),
      child: Container(
        width: AppDimensions.standardNavWidth,
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          height: 48,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: isSelected 
              ? theme.colorScheme.primary.withOpacity(0.1)
              : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(
                isSelected ? activeIcon : icon,
                color: isSelected 
                  ? theme.colorScheme.primary 
                  : (isDark ? Colors.grey.shade400 : Colors.grey.shade600),
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: isSelected 
                    ? theme.colorScheme.primary 
                    : (isDark ? Colors.grey.shade400 : Colors.grey.shade600),
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 主题切换按钮
  Widget _buildThemeToggleButton(BuildContext context, {required bool isCompact}) {
    final theme = Theme.of(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDark = theme.brightness == Brightness.dark;
    
    if (isCompact) {
      // 紧凑模式：仅图标
      return Tooltip(
        message: themeProvider.isDarkMode ? '切换到日间模式' : '切换到夜间模式',
        child: InkWell(
          onTap: () => themeProvider.toggleTheme(),
          child: Container(
            width: AppDimensions.compactNavWidth,
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              height: 48,
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                color: isDark ? Colors.grey.shade400 : Colors.grey.shade600,
                size: 24,
              ),
            ),
          ),
        ),
      );
    } else {
      // 扩展模式：图标+文字
      return InkWell(
        onTap: () => themeProvider.toggleTheme(),
        child: Container(
          width: AppDimensions.standardNavWidth,
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: 48,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                  color: isDark ? Colors.grey.shade400 : Colors.grey.shade600,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  themeProvider.isDarkMode ? '日间模式' : '夜间模式',
                  style: TextStyle(
                    fontSize: 14,
                    color: isDark ? Colors.grey.shade400 : Colors.grey.shade600,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
  }
}