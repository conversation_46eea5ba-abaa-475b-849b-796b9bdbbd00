import 'package:flutter/material.dart';
import '../../constants/dimensions.dart';
import '../../constants/colors.dart';

/// 自定义按钮组件
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final Widget? icon;
  final bool isLoading;
  final bool isExpanded;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? borderRadius;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isExpanded = false,
    this.backgroundColor,
    this.foregroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    Widget button = _buildButton(context, theme);
    
    if (isExpanded) {
      button = SizedBox(
        width: double.infinity,
        child: button,
      );
    }
    
    return button;
  }

  Widget _buildButton(BuildContext context, ThemeData theme) {
    final buttonStyle = _getButtonStyle(theme);
    
    Widget child = isLoading 
      ? _buildLoadingContent()
      : _buildButtonContent();

    switch (type) {
      case ButtonType.primary:
        return ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: child,
        );
      case ButtonType.secondary:
        return OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: child,
        );
      case ButtonType.text:
        return TextButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: child,
        );
    }
  }

  Widget _buildButtonContent() {
    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon!,
          const SizedBox(width: AppDimensions.paddingS),
          Text(text),
        ],
      );
    }
    return Text(text);
  }

  Widget _buildLoadingContent() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: _getLoadingSize(),
          height: _getLoadingSize(),
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              type == ButtonType.primary ? Colors.white : AppColors.lightAccentColor,
            ),
          ),
        ),
        const SizedBox(width: AppDimensions.paddingS),
        Text(text),
      ],
    );
  }

  ButtonStyle _getButtonStyle(ThemeData theme) {
    final padding = _getPadding();
    final textStyle = _getTextStyle(theme);
    final shape = RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(borderRadius ?? AppDimensions.radiusL),
    );

    switch (type) {
      case ButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? AppColors.lightAccentColor,
          foregroundColor: foregroundColor ?? Colors.white,
          padding: padding,
          textStyle: textStyle,
          shape: shape,
          elevation: 2,
        );
      case ButtonType.secondary:
        return OutlinedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor ?? theme.colorScheme.primary,
          padding: padding,
          textStyle: textStyle,
          shape: shape,
          side: BorderSide(
            color: backgroundColor ?? theme.colorScheme.primary,
            width: 1.5,
          ),
        );
      case ButtonType.text:
        return TextButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor ?? theme.colorScheme.primary,
          padding: padding,
          textStyle: textStyle,
          shape: shape,
        );
    }
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(
          vertical: AppDimensions.paddingS,
          horizontal: AppDimensions.paddingM,
        );
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(
          vertical: AppDimensions.paddingM,
          horizontal: AppDimensions.paddingXL,
        );
      case ButtonSize.large:
        return const EdgeInsets.symmetric(
          vertical: AppDimensions.paddingL,
          horizontal: AppDimensions.paddingXXL,
        );
    }
  }

  TextStyle _getTextStyle(ThemeData theme) {
    switch (size) {
      case ButtonSize.small:
        return theme.textTheme.labelMedium!;
      case ButtonSize.medium:
        return theme.textTheme.labelLarge!;
      case ButtonSize.large:
        return theme.textTheme.titleMedium!;
    }
  }

  double _getLoadingSize() {
    switch (size) {
      case ButtonSize.small:
        return 14;
      case ButtonSize.medium:
        return 16;
      case ButtonSize.large:
        return 18;
    }
  }
}

/// 按钮类型
enum ButtonType {
  primary,
  secondary,
  text,
}

/// 按钮尺寸
enum ButtonSize {
  small,
  medium,
  large,
}