/// DateTime 扩展方法
extension DateExtensions on DateTime {
  /// 格式化为时间字符串 (HH:mm)
  String toTimeString() {
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  /// 格式化为日期字符串 (MM月dd日)
  String toDateString() {
    return '${month}月${day}日';
  }

  /// 格式化为完整日期时间字符串 (yyyy年MM月dd日 HH:mm)
  String toFullDateTimeString() {
    return '${year}年${month}月${day}日 ${toTimeString()}';
  }

  /// 获取相对时间描述
  String toRelativeString() {
    final now = DateTime.now();
    final difference = now.difference(this);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return '刚刚';
        } else {
          return '${difference.inMinutes}分钟前';
        }
      } else {
        return '${difference.inHours}小时前';
      }
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '${weeks}周前';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '${months}个月前';
    } else {
      final years = (difference.inDays / 365).floor();
      return '${years}年前';
    }
  }

  /// 获取聊天时间显示格式
  String toChatTimeString() {
    final now = DateTime.now();
    final difference = now.difference(this);

    if (difference.inDays == 0) {
      return toTimeString();
    } else if (difference.inDays == 1) {
      return '昨天 ${toTimeString()}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return toDateString();
    }
  }

  /// 判断是否为今天
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }

  /// 判断是否为昨天
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year && month == yesterday.month && day == yesterday.day;
  }

  /// 判断是否为本周
  bool get isThisWeek {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return isAfter(startOfWeek.subtract(const Duration(days: 1))) && 
           isBefore(endOfWeek.add(const Duration(days: 1)));
  }
}