import '../../domain/services/chat_service.dart';
import '../models/chat_message.dart';
import '../models/chat_conversation.dart';
import '../models/agent_info.dart';
import '../models/bazi_option.dart';

/// 聊天仓库实现
class ChatRepository implements ChatService {
  // 模拟数据存储
  final Map<String, ChatConversation> _conversations = {};
  final List<String> _quickActions = [
    '今日运势如何？',
    '事业发展建议',
    '感情状况分析',
    '健康注意事项',
    '财运分析',
    '人际关系指导',
  ];

  @override
  Future<ChatMessage> sendMessage({
    required String conversationId,
    required String content,
    required String agentId,
    List<String>? baziIds,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300)); // 模拟网络延迟

      final message = ChatMessage.user(
        content: content,
        agentName: _getAgentName(agentId),
        metadata: metadata,
      );

      // 更新对话
      final conversation = _conversations[conversationId];
      if (conversation != null) {
        _conversations[conversationId] = conversation.addMessage(message);
      }

      return message;
    } catch (e) {
      throw ChatException('发送消息失败: $e');
    }
  }

  @override
  Future<ChatMessage> getAiResponse({
    required String conversationId,
    required ChatMessage userMessage,
    required String agentId,
    List<String>? baziIds,
  }) async {
    try {
      // 模拟AI处理时间
      await Future.delayed(const Duration(milliseconds: 800));

      // 生成模拟回复
      final responseContent = _generateMockResponse(userMessage.content, agentId);
      
      final aiMessage = ChatMessage.ai(
        content: responseContent,
        agentName: _getAgentName(agentId),
        status: MessageStatus.sent,
      );

      // 更新对话
      final conversation = _conversations[conversationId];
      if (conversation != null) {
        _conversations[conversationId] = conversation.addMessage(aiMessage);
      }

      return aiMessage;
    } catch (e) {
      throw ChatException('获取AI回复失败: $e');
    }
  }

  @override
  Future<ChatConversation> createConversation({
    String? title,
    String? agentId,
    List<String>? baziIds,
  }) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));

      final now = DateTime.now();
      final conversation = ChatConversation(
        id: now.millisecondsSinceEpoch.toString(),
        title: title ?? '新对话',
        messages: [],
        createdAt: now,
        updatedAt: now,
        agentId: agentId,
        baziIds: baziIds,
      );

      _conversations[conversation.id] = conversation;
      return conversation;
    } catch (e) {
      throw ChatException('创建对话失败: $e');
    }
  }

  @override
  Future<List<ChatConversation>> getConversations({
    int limit = 50,
    int offset = 0,
    String? agentId,
  }) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));

      var conversations = _conversations.values.toList();
      
      // 按更新时间排序
      conversations.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

      // 筛选助手
      if (agentId != null) {
        conversations = conversations.where((c) => c.agentId == agentId).toList();
      }

      // 分页
      final startIndex = offset;
      final endIndex = (startIndex + limit).clamp(0, conversations.length);
      
      return conversations.sublist(startIndex, endIndex);
    } catch (e) {
      throw ChatException('获取对话列表失败: $e');
    }
  }

  @override
  Future<ChatConversation?> getConversation(String conversationId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 100));
      return _conversations[conversationId];
    } catch (e) {
      throw ChatException('获取对话失败: $e');
    }
  }

  @override
  Future<ChatConversation> updateConversation(ChatConversation conversation) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      
      final updatedConversation = conversation.copyWith(updatedAt: DateTime.now());
      _conversations[conversation.id] = updatedConversation;
      
      return updatedConversation;
    } catch (e) {
      throw ChatException('更新对话失败: $e');
    }
  }

  @override
  Future<void> deleteConversation(String conversationId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      _conversations.remove(conversationId);
    } catch (e) {
      throw ChatException('删除对话失败: $e');
    }
  }

  @override
  Future<void> deleteMessage(String conversationId, String messageId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 100));
      
      final conversation = _conversations[conversationId];
      if (conversation != null) {
        _conversations[conversationId] = conversation.removeMessage(messageId);
      }
    } catch (e) {
      throw ChatException('删除消息失败: $e');
    }
  }

  @override
  Future<void> clearConversation(String conversationId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      
      final conversation = _conversations[conversationId];
      if (conversation != null) {
        _conversations[conversationId] = conversation.copyWith(
          messages: [],
          updatedAt: DateTime.now(),
        );
      }
    } catch (e) {
      throw ChatException('清空对话失败: $e');
    }
  }

  @override
  Future<List<ChatMessage>> searchMessages({
    required String query,
    String? conversationId,
    String? agentId,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 20,
  }) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      List<ChatMessage> allMessages = [];
      
      // 收集所有消息
      for (final conversation in _conversations.values) {
        if (conversationId != null && conversation.id != conversationId) continue;
        if (agentId != null && conversation.agentId != agentId) continue;
        
        allMessages.addAll(conversation.messages);
      }

      // 按时间筛选
      if (startDate != null) {
        allMessages = allMessages.where((m) => m.timestamp.isAfter(startDate)).toList();
      }
      if (endDate != null) {
        allMessages = allMessages.where((m) => m.timestamp.isBefore(endDate)).toList();
      }

      // 内容搜索
      final searchResults = allMessages.where((message) {
        return message.content.toLowerCase().contains(query.toLowerCase());
      }).toList();

      // 按时间排序并限制数量
      searchResults.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return searchResults.take(limit).toList();
    } catch (e) {
      throw ChatException('搜索消息失败: $e');
    }
  }

  @override
  Future<List<AgentInfo>> getAgents() async {
    try {
      await Future.delayed(const Duration(milliseconds: 100));
      return PredefinedAgents.getActiveAgents();
    } catch (e) {
      throw ChatException('获取助手列表失败: $e');
    }
  }

  @override
  Future<List<BaziOption>> getBaziOptions() async {
    try {
      await Future.delayed(const Duration(milliseconds: 100));
      return PredefinedBaziOptions.getActiveOptions();
    } catch (e) {
      throw ChatException('获取八字选项失败: $e');
    }
  }

  @override
  Future<BaziOption> createBaziOption(BaziOption baziOption) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      
      // 这里应该调用API创建八字选项
      // 目前返回模拟数据
      return baziOption.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    } catch (e) {
      throw ChatException('创建八字选项失败: $e');
    }
  }

  @override
  Future<BaziOption> updateBaziOption(BaziOption baziOption) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      
      return baziOption.copyWith(updatedAt: DateTime.now());
    } catch (e) {
      throw ChatException('更新八字选项失败: $e');
    }
  }

  @override
  Future<void> deleteBaziOption(String baziId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      // 这里应该调用API删除八字选项
    } catch (e) {
      throw ChatException('删除八字选项失败: $e');
    }
  }

  @override
  Future<List<String>> getQuickActions({String? agentId}) async {
    try {
      await Future.delayed(const Duration(milliseconds: 100));
      
      // 可以根据agentId返回不同的快捷操作
      if (agentId != null) {
        switch (agentId) {
          case 'career':
            return ['职业规划建议', '升职加薪指导', '跳槽时机分析', '创业方向建议'];
          case 'emotion':
            return ['恋爱运势分析', '婚姻状况指导', '情感问题解答', '桃花运势预测'];
          case 'health':
            return ['健康状况分析', '养生建议', '疾病预防指导', '体质调理方案'];
          default:
            return _quickActions;
        }
      }
      
      return _quickActions;
    } catch (e) {
      throw ChatException('获取快捷操作失败: $e');
    }
  }

  @override
  Future<String> exportConversation(String conversationId, {ExportFormat format = ExportFormat.text}) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      final conversation = _conversations[conversationId];
      if (conversation == null) {
        throw ChatException('对话不存在');
      }

      switch (format) {
        case ExportFormat.text:
          return _exportAsText(conversation);
        case ExportFormat.json:
          return _exportAsJson(conversation);
        case ExportFormat.markdown:
          return _exportAsMarkdown(conversation);
        case ExportFormat.pdf:
          throw ChatException('PDF导出暂未实现');
      }
    } catch (e) {
      throw ChatException('导出对话失败: $e');
    }
  }

  @override
  Future<ChatConversation> importConversation(String data, {ExportFormat format = ExportFormat.text}) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 这里应该解析导入的数据
      // 目前返回模拟数据
      throw ChatException('导入功能暂未实现');
    } catch (e) {
      throw ChatException('导入对话失败: $e');
    }
  }

  @override
  Future<ChatStats> getChatStats({
    String? agentId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      
      var conversations = _conversations.values.toList();
      if (agentId != null) {
        conversations = conversations.where((c) => c.agentId == agentId).toList();
      }

      final allMessages = conversations.expand((c) => c.messages).toList();
      
      // 按时间筛选消息
      var filteredMessages = allMessages;
      if (startDate != null) {
        filteredMessages = filteredMessages.where((m) => m.timestamp.isAfter(startDate)).toList();
      }
      if (endDate != null) {
        filteredMessages = filteredMessages.where((m) => m.timestamp.isBefore(endDate)).toList();
      }

      final userMessages = filteredMessages.where((m) => m.isUser).length;
      final aiMessages = filteredMessages.where((m) => !m.isUser).length;

      return ChatStats(
        totalConversations: conversations.length,
        totalMessages: filteredMessages.length,
        userMessages: userMessages,
        aiMessages: aiMessages,
        agentUsage: _calculateAgentUsage(filteredMessages),
        dailyMessages: _calculateDailyMessages(filteredMessages),
        averageResponseTime: 1.2, // 模拟数据
        firstMessageTime: filteredMessages.isNotEmpty 
            ? filteredMessages.map((m) => m.timestamp).reduce((a, b) => a.isBefore(b) ? a : b)
            : null,
        lastMessageTime: filteredMessages.isNotEmpty
            ? filteredMessages.map((m) => m.timestamp).reduce((a, b) => a.isAfter(b) ? a : b)
            : null,
      );
    } catch (e) {
      throw ChatException('获取统计信息失败: $e');
    }
  }

  @override
  Future<void> markMessageAsRead(String conversationId, String messageId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 100));
      // 这里应该标记消息为已读
    } catch (e) {
      throw ChatException('标记消息已读失败: $e');
    }
  }

  @override
  Future<void> pinConversation(String conversationId, bool isPinned) async {
    try {
      await Future.delayed(const Duration(milliseconds: 100));
      
      final conversation = _conversations[conversationId];
      if (conversation != null) {
        _conversations[conversationId] = conversation.copyWith(
          isPinned: isPinned,
          updatedAt: DateTime.now(),
        );
      }
    } catch (e) {
      throw ChatException('设置对话置顶失败: $e');
    }
  }

  @override
  Future<List<ChatMessage>> getMessageHistory({
    String? conversationId,
    int limit = 100,
    String? cursor,
  }) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));
      
      if (conversationId != null) {
        final conversation = _conversations[conversationId];
        if (conversation != null) {
          return conversation.messages.take(limit).toList();
        }
      }
      
      // 返回所有消息
      final allMessages = _conversations.values
          .expand((c) => c.messages)
          .toList();
          
      allMessages.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return allMessages.take(limit).toList();
    } catch (e) {
      throw ChatException('获取历史记录失败: $e');
    }
  }

  // 私有辅助方法
  String _getAgentName(String agentId) {
    final agent = PredefinedAgents.getById(agentId);
    return agent?.name ?? '智能助手';
  }

  String _generateMockResponse(String userMessage, String agentId) {
    final responses = {
      'bazi_master': [
        '根据您的八字命盘分析，我发现您近期的运势呈现上升趋势。特别是在事业方面，将有新的机遇出现...',
        '从您的命理格局来看，您天生具有领导才能，适合从事管理类工作。建议您在工作中多展现自己的组织能力...',
        '您的财运在本月中下旬会有明显改善，建议您抓住这个时机，合理规划投资方向...',
      ],
      'career': [
        '基于您当前的职业状况，我建议您可以考虑在技能提升方面多投入一些时间和精力...',
        '您的职业发展轨迹显示出很好的上升潜力，建议您主动寻求更多的项目参与机会...',
        '从行业趋势来看，您所在的领域正处于快速发展期，这是一个很好的机会...',
      ],
      'emotion': [
        '在感情方面，我建议您要更多地关注沟通的技巧，学会倾听对方的想法...',
        '您的情感运势显示，近期可能会遇到一些小的摩擦，但这也是增进了解的好机会...',
        '建议您在处理感情问题时要保持耐心，真诚的态度是解决问题的关键...',
      ],
      'health': [
        '从您的体质特点来看，建议您在饮食方面要注意清淡，多吃新鲜蔬菜水果...',
        '您的身体状况整体良好，但要注意劳逸结合，避免过度疲劳...',
        '建议您可以尝试一些舒缓的运动，如太极、瑜伽等，有助于身心健康...',
      ],
    };

    final agentResponses = responses[agentId] ?? responses['bazi_master']!;
    return agentResponses[DateTime.now().millisecond % agentResponses.length];
  }

  String _exportAsText(ChatConversation conversation) {
    final buffer = StringBuffer();
    buffer.writeln('对话标题: ${conversation.title}');
    buffer.writeln('创建时间: ${conversation.createdAt}');
    buffer.writeln('更新时间: ${conversation.updatedAt}');
    buffer.writeln('消息数量: ${conversation.messageCount}');
    buffer.writeln('---');
    
    for (final message in conversation.messages) {
      final sender = message.isUser ? '用户' : message.agentName;
      buffer.writeln('[$sender] ${message.timestamp}');
      buffer.writeln(message.content);
      buffer.writeln();
    }
    
    return buffer.toString();
  }

  String _exportAsJson(ChatConversation conversation) {
    // 这里应该使用JSON编码
    return conversation.toJson().toString();
  }

  String _exportAsMarkdown(ChatConversation conversation) {
    final buffer = StringBuffer();
    buffer.writeln('# ${conversation.title}');
    buffer.writeln();
    buffer.writeln('**创建时间:** ${conversation.createdAt}');
    buffer.writeln('**更新时间:** ${conversation.updatedAt}');
    buffer.writeln('**消息数量:** ${conversation.messageCount}');
    buffer.writeln();
    buffer.writeln('---');
    buffer.writeln();
    
    for (final message in conversation.messages) {
      final sender = message.isUser ? '**用户**' : '**${message.agentName}**';
      buffer.writeln('## $sender');
      buffer.writeln();
      buffer.writeln(message.content);
      buffer.writeln();
      buffer.writeln('*时间: ${message.timestamp}*');
      buffer.writeln();
    }
    
    return buffer.toString();
  }

  Map<String, int> _calculateAgentUsage(List<ChatMessage> messages) {
    final usage = <String, int>{};
    for (final message in messages) {
      if (!message.isUser) {
        usage[message.agentName] = (usage[message.agentName] ?? 0) + 1;
      }
    }
    return usage;
  }

  Map<String, int> _calculateDailyMessages(List<ChatMessage> messages) {
    final daily = <String, int>{};
    for (final message in messages) {
      final dateKey = '${message.timestamp.year}-${message.timestamp.month.toString().padLeft(2, '0')}-${message.timestamp.day.toString().padLeft(2, '0')}';
      daily[dateKey] = (daily[dateKey] ?? 0) + 1;
    }
    return daily;
  }
}

/// 聊天异常类
class ChatException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const ChatException(this.message, {this.code, this.originalError});

  @override
  String toString() {
    return 'ChatException: $message${code != null ? ' (code: $code)' : ''}';
  }
}