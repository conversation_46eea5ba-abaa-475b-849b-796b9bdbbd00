import 'package:flutter/material.dart';
import '../constants/dimensions.dart';

/// UI辅助工具类
class UiHelpers {
  UiHelpers._();

  /// 显示对话框
  static Future<void> showAppDialog(BuildContext context, Widget page) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusXXL),
          ),
          child: ConstrainedBox(
            constraints: const BoxConstraints(
              maxWidth: 600, 
              maxHeight: 700
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppDimensions.radiusXXL),
              child: page,
            ),
          ),
        );
      },
    );
  }

  /// 显示确认对话框
  static Future<bool?> showConfirmDialog(
    BuildContext context, {
    required String title,
    required String content,
    String confirmText = '确定',
    String cancelText = '取消',
    Color? confirmColor,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(content),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(cancelText),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: confirmColor != null 
                ? ElevatedButton.styleFrom(backgroundColor: confirmColor)
                : null,
              child: Text(confirmText),
            ),
          ],
        );
      },
    );
  }

  /// 显示底部选择器
  static Future<T?> showBottomPicker<T>(
    BuildContext context, {
    required String title,
    required List<T> items,
    required String Function(T) itemBuilder,
    T? selectedItem,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppDimensions.radiusXL),
        ),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(AppDimensions.paddingXXL),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: AppDimensions.paddingL),
              ...items.map((item) {
                final isSelected = item == selectedItem;
                return ListTile(
                  title: Text(itemBuilder(item)),
                  selected: isSelected,
                  onTap: () => Navigator.of(context).pop(item),
                );
              }),
              const SizedBox(height: AppDimensions.paddingL),
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 创建渐变背景
  static Widget createGradientBackground({
    required Widget child,
    List<Color>? colors,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: begin,
          end: end,
          colors: colors ?? [
            Colors.blue.shade50,
            Colors.purple.shade50,
          ],
        ),
      ),
      child: child,
    );
  }

  /// 创建阴影效果
  static List<BoxShadow> createShadow({
    Color? color,
    double? blurRadius,
    double? spreadRadius,
    Offset? offset,
  }) {
    return [
      BoxShadow(
        color: color ?? Colors.black.withOpacity(0.1),
        blurRadius: blurRadius ?? AppDimensions.shadowBlurRadius,
        spreadRadius: spreadRadius ?? 0,
        offset: offset ?? const Offset(0, AppDimensions.shadowOffset),
      ),
    ];
  }

  /// 安全的异步操作包装
  static Future<void> safeAsyncOperation(Future<void> Function() operation) async {
    try {
      await operation();
    } catch (e) {
      debugPrint('异步操作出错: $e');
    }
  }
}