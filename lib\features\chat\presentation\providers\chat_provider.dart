import 'package:flutter/material.dart';
import '../../domain/services/chat_service.dart';
import '../../data/models/chat_message.dart';
import '../../data/models/chat_conversation.dart';
import '../../data/models/agent_info.dart';
import '../../data/models/bazi_option.dart';
import '../../data/repositories/chat_repository.dart';

/// 聊天状态管理
class ChatProvider extends ChangeNotifier {
  final ChatService _chatService = ChatRepository();
  
  // 状态变量
  List<ChatConversation> _conversations = [];
  String? _currentConversationId;
  List<AgentInfo> _agents = [];
  List<BaziOption> _baziOptions = [];
  List<String> _quickActions = [];
  
  String _selectedAgentId = 'bazi_master';
  List<String> _selectedBaziIds = [];
  bool _isMultiSelectMode = false;
  bool _isTyping = false;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<ChatConversation> get conversations => _conversations;
  ChatConversation? get currentConversation => 
      _currentConversationId != null 
          ? _conversations.where((c) => c.id == _currentConversationId).firstOrNull
          : null;
  List<ChatMessage> get currentMessages => currentConversation?.messages ?? [];
  List<AgentInfo> get agents => _agents;
  List<BaziOption> get baziOptions => _baziOptions;
  List<String> get quickActions => _quickActions;
  
  String get selectedAgentId => _selectedAgentId;
  AgentInfo? get selectedAgent => _agents.where((a) => a.id == _selectedAgentId).firstOrNull;
  List<String> get selectedBaziIds => _selectedBaziIds;
  List<BaziOption> get selectedBaziOptions => 
      _selectedBaziIds.map((id) => _baziOptions.where((b) => b.id == id).firstOrNull)
          .where((b) => b != null)
          .cast<BaziOption>()
          .toList();
  
  bool get isMultiSelectMode => _isMultiSelectMode;
  bool get isTyping => _isTyping;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasCurrentConversation => _currentConversationId != null;

  /// 初始化
  Future<void> initialize() async {
    _setLoading(true);
    try {
      await Future.wait([
        _loadConversations(),
        _loadAgents(),
        _loadBaziOptions(),
        _loadQuickActions(),
      ]);
      
      // 如果没有对话，创建一个默认对话
      if (_conversations.isEmpty) {
        await createNewConversation();
      } else {
        _currentConversationId = _conversations.first.id;
      }
      
      _clearError();
    } catch (e) {
      _setError('初始化失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 发送消息
  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty || _currentConversationId == null) return;
    
    _clearError();
    
    try {
      // 发送用户消息
      final userMessage = await _chatService.sendMessage(
        conversationId: _currentConversationId!,
        content: content.trim(),
        agentId: _selectedAgentId,
        baziIds: _selectedBaziIds.isNotEmpty ? _selectedBaziIds : null,
      );

      // 更新本地状态
      _updateConversationWithMessage(_currentConversationId!, userMessage);
      
      // 设置正在输入状态
      _setTyping(true);
      
      // 获取AI回复
      final aiMessage = await _chatService.getAiResponse(
        conversationId: _currentConversationId!,
        userMessage: userMessage,
        agentId: _selectedAgentId,
        baziIds: _selectedBaziIds.isNotEmpty ? _selectedBaziIds : null,
      );
      
      // 更新本地状态
      _updateConversationWithMessage(_currentConversationId!, aiMessage);
      
    } catch (e) {
      _setError('发送消息失败: $e');
    } finally {
      _setTyping(false);
    }
  }

  /// 创建新对话
  Future<void> createNewConversation({String? title}) async {
    _clearError();
    
    try {
      final conversation = await _chatService.createConversation(
        title: title,
        agentId: _selectedAgentId,
        baziIds: _selectedBaziIds.isNotEmpty ? _selectedBaziIds : null,
      );
      
      _conversations.insert(0, conversation);
      _currentConversationId = conversation.id;
      notifyListeners();
    } catch (e) {
      _setError('创建对话失败: $e');
    }
  }

  /// 切换对话
  Future<void> switchConversation(String conversationId) async {
    if (_currentConversationId == conversationId) return;
    
    _currentConversationId = conversationId;
    notifyListeners();
    
    // 可以在这里加载对话的详细信息
    try {
      final conversation = await _chatService.getConversation(conversationId);
      if (conversation != null) {
        final index = _conversations.indexWhere((c) => c.id == conversationId);
        if (index != -1) {
          _conversations[index] = conversation;
          notifyListeners();
        }
      }
    } catch (e) {
      _setError('加载对话失败: $e');
    }
  }

  /// 删除对话
  Future<void> deleteConversation(String conversationId) async {
    if (_conversations.length <= 1) return; // 至少保留一个对话
    
    _clearError();
    
    try {
      await _chatService.deleteConversation(conversationId);
      
      _conversations.removeWhere((c) => c.id == conversationId);
      
      // 如果删除的是当前对话，切换到第一个对话
      if (_currentConversationId == conversationId) {
        _currentConversationId = _conversations.isNotEmpty ? _conversations.first.id : null;
      }
      
      notifyListeners();
    } catch (e) {
      _setError('删除对话失败: $e');
    }
  }

  /// 清空对话
  Future<void> clearConversation(String conversationId) async {
    _clearError();
    
    try {
      await _chatService.clearConversation(conversationId);
      
      final index = _conversations.indexWhere((c) => c.id == conversationId);
      if (index != -1) {
        _conversations[index] = _conversations[index].copyWith(
          messages: [],
          updatedAt: DateTime.now(),
        );
        notifyListeners();
      }
    } catch (e) {
      _setError('清空对话失败: $e');
    }
  }

  /// 选择助手
  void selectAgent(String agentId) {
    if (_selectedAgentId != agentId) {
      _selectedAgentId = agentId;
      _loadQuickActions(); // 重新加载快捷操作
      notifyListeners();
    }
  }

  /// 切换八字选择状态
  void toggleBaziSelection(String baziId) {
    if (_selectedBaziIds.contains(baziId)) {
      _selectedBaziIds.remove(baziId);
    } else {
      if (_isMultiSelectMode) {
        _selectedBaziIds.add(baziId);
      } else {
        _selectedBaziIds = [baziId];
      }
    }
    notifyListeners();
  }
  
  /// 选择八字
  void selectBazi(String baziId) {
    final bazi = _baziOptions.where((b) => b.id == baziId).firstOrNull;
    if (bazi == null) return;
    
    if (bazi.isAddNew) {
      // 处理添加新八字
      _handleAddNewBazi();
      return;
    }
    
    if (_isMultiSelectMode) {
      if (_selectedBaziIds.contains(baziId)) {
        _selectedBaziIds.remove(baziId);
      } else {
        _selectedBaziIds.add(baziId);
      }
    } else {
      _selectedBaziIds = [baziId];
    }
    
    notifyListeners();
  }

  /// 移除选中的八字
  void removeBazi(String baziId) {
    _selectedBaziIds.remove(baziId);
    notifyListeners();
  }

  /// 切换多选模式
  void toggleMultiSelectMode() {
    _isMultiSelectMode = !_isMultiSelectMode;
    
    // 如果切换到单选模式且选中了多个八字，只保留第一个
    if (!_isMultiSelectMode && _selectedBaziIds.length > 1) {
      _selectedBaziIds = _selectedBaziIds.take(1).toList();
    }
    
    notifyListeners();
  }

  /// 搜索消息
  Future<List<ChatMessage>> searchMessages(String query) async {
    if (query.trim().isEmpty) return [];
    
    try {
      return await _chatService.searchMessages(
        query: query.trim(),
        conversationId: _currentConversationId,
      );
    } catch (e) {
      _setError('搜索失败: $e');
      return [];
    }
  }

  /// 导出对话
  Future<String?> exportConversation(String conversationId, {ExportFormat format = ExportFormat.text}) async {
    try {
      return await _chatService.exportConversation(conversationId, format: format);
    } catch (e) {
      _setError('导出失败: $e');
      return null;
    }
  }

  /// 置顶对话
  Future<void> pinConversation(String conversationId, bool isPinned) async {
    try {
      await _chatService.pinConversation(conversationId, isPinned);
      
      final index = _conversations.indexWhere((c) => c.id == conversationId);
      if (index != -1) {
        _conversations[index] = _conversations[index].copyWith(
          isPinned: isPinned,
          updatedAt: DateTime.now(),
        );
        
        // 重新排序对话列表
        _sortConversations();
        notifyListeners();
      }
    } catch (e) {
      _setError('设置置顶失败: $e');
    }
  }

  /// 重新加载对话列表
  Future<void> refreshConversations() async {
    await _loadConversations();
  }

  /// 清除错误
  void clearError() {
    _clearError();
  }

  // 私有方法
  Future<void> _loadConversations() async {
    final conversations = await _chatService.getConversations();
    _conversations = conversations;
    _sortConversations();
  }

  Future<void> _loadAgents() async {
    _agents = await _chatService.getAgents();
    
    // 如果当前选中的助手不在列表中，选择第一个
    if (!_agents.any((a) => a.id == _selectedAgentId) && _agents.isNotEmpty) {
      _selectedAgentId = _agents.first.id;
    }
  }

  Future<void> _loadBaziOptions() async {
    _baziOptions = await _chatService.getBaziOptions();
    
    // 如果当前选中的八字不在列表中，清空选择
    _selectedBaziIds = _selectedBaziIds.where((id) => 
        _baziOptions.any((b) => b.id == id)).toList();
    
    // 如果没有选中任何八字且有默认八字，选择默认八字
    if (_selectedBaziIds.isEmpty) {
      final defaultBazi = _baziOptions.where((b) => b.isDefault && !b.isAddNew).firstOrNull;
      if (defaultBazi != null) {
        _selectedBaziIds = [defaultBazi.id];
      }
    }
  }

  Future<void> _loadQuickActions() async {
    _quickActions = await _chatService.getQuickActions(agentId: _selectedAgentId);
  }

  void _updateConversationWithMessage(String conversationId, ChatMessage message) {
    final index = _conversations.indexWhere((c) => c.id == conversationId);
    if (index != -1) {
      _conversations[index] = _conversations[index].addMessage(message);
      notifyListeners();
    }
  }

  void _sortConversations() {
    _conversations.sort((a, b) {
      // 置顶的对话排在前面
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      
      // 按更新时间排序
      return b.updatedAt.compareTo(a.updatedAt);
    });
  }

  void _handleAddNewBazi() {
    // TODO: 打开添加新八字的对话框
    _setError('添加新八字功能正在开发中');
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setTyping(bool typing) {
    _isTyping = typing;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }
}