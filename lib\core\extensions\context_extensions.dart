import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

/// BuildContext 扩展方法
extension ContextExtensions on BuildContext {
  // 主题相关
  ThemeData get theme => Theme.of(this);
  ColorScheme get colorScheme => theme.colorScheme;
  TextTheme get textTheme => theme.textTheme;
  bool get isDarkMode => theme.brightness == Brightness.dark;

  // 屏幕尺寸相关
  MediaQueryData get mediaQuery => MediaQuery.of(this);
  Size get screenSize => mediaQuery.size;
  double get screenWidth => screenSize.width;
  double get screenHeight => screenSize.height;
  EdgeInsets get padding => mediaQuery.padding;
  EdgeInsets get viewInsets => mediaQuery.viewInsets;

  // 响应式断点判断
  bool get isMobile => screenWidth < AppConstants.mobileBreakpoint;
  bool get isTablet => screenWidth >= AppConstants.mobileBreakpoint && 
                      screenWidth < AppConstants.tabletBreakpoint;
  bool get isDesktop => screenWidth >= AppConstants.tabletBreakpoint;
  bool get isLargeDesktop => screenWidth >= AppConstants.desktopBreakpoint;

  // 导航相关
  NavigatorState get navigator => Navigator.of(this);
  void pop<T extends Object?>([T? result]) => navigator.pop(result);
  Future<T?> push<T extends Object?>(Route<T> route) => navigator.push(route);
  Future<T?> pushNamed<T extends Object?>(String routeName, {Object? arguments}) =>
      navigator.pushNamed(routeName, arguments: arguments);
  Future<T?> pushReplacement<T extends Object?, TO extends Object?>(
    Route<T> newRoute, {TO? result}) => navigator.pushReplacement(newRoute, result: result);
  Future<T?> pushReplacementNamed<T extends Object?, TO extends Object?>(
    String routeName, {TO? result, Object? arguments}) =>
      navigator.pushReplacementNamed(routeName, result: result, arguments: arguments);

  // SnackBar 便捷方法
  void showSnackBar(String message, {Duration? duration, Color? backgroundColor}) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: duration ?? const Duration(seconds: 3),
        backgroundColor: backgroundColor,
      ),
    );
  }

  // 错误提示
  void showErrorSnackBar(String message) {
    showSnackBar(message, backgroundColor: colorScheme.error);
  }

  // 成功提示
  void showSuccessSnackBar(String message) {
    showSnackBar(message, backgroundColor: Colors.green);
  }

  // 隐藏键盘
  void hideKeyboard() {
    FocusScope.of(this).unfocus();
  }
}