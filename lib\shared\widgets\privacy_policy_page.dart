import 'package:flutter/material.dart';
import '../../core/constants/dimensions.dart';

/// 隐私政策页面
class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('隐私协议'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '最后更新日期：2025年7月30日',
              style: theme.textTheme.bodySmall,
            ),
            const SizedBox(height: AppDimensions.paddingL),
            _buildSectionTitle(theme, '引言'),
            _buildSectionContent(theme, '''欢迎使用八字命理应用。我们深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。'''),
            const SizedBox(height: AppDimensions.paddingL),
            _buildSectionTitle(theme, '我们如何收集和使用您的个人信息'),
            _buildSectionContent(theme, '''我们会出于本政策所述的以下目的，收集和使用您的个人信息：
1. 为向您提供服务，您需要授权我们收集和使用必要的个人信息。如您拒绝提供，您将无法正常使用我们的相关服务。
2. 我们可能会收集您的设备信息、日志信息、位置信息等，以用于改善我们的产品和服务。
3. 您在使用我们的服务时，我们可能会使用您的手机号码用于身份验证、安全验证、客户服务等。
4. 我们需要您提供出生日期、时间等信息用于八字排盘和命理分析，这些信息将被严格保密。'''),
            const SizedBox(height: AppDimensions.paddingL),
            _buildSectionTitle(theme, '我们如何共享、转让、公开披露您的个人信息'),
            _buildSectionContent(theme, '''我们不会与任何公司、组织和个人分享您的个人信息，但以下情况除外：
1. 在获取明确同意的情况下共享。
2. 我们可能会根据法律法规规定，或按政府主管部门的强制性要求，对外共享您的个人信息。
3. 在法律法规允许的范围内，为维护我们、我们的关联方或合作伙伴、您或其他用户或社会公众利益、财产或安全免遭损害而有必要提供。'''),
            const SizedBox(height: AppDimensions.paddingL),
            _buildSectionTitle(theme, '您如何管理您的个人信息'),
            _buildSectionContent(theme, '''您可以通过我们的应用访问和管理您的个人信息。如果您希望查询、更正或删除您的个人信息，或撤回您的授权同意，请通过应用内的指引操作或联系我们的客服。'''),
            const SizedBox(height: AppDimensions.paddingL),
            _buildSectionTitle(theme, '联系我们'),
            _buildSectionContent(theme, '''如果您对本隐私政策有任何疑问、意见或建议，请通过 [联系方式] 与我们联系。'''),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(ThemeData theme, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.paddingS),
      child: Text(
        title,
        style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildSectionContent(ThemeData theme, String content) {
    return Text(
      content,
      style: theme.textTheme.bodyMedium?.copyWith(height: 1.6),
    );
  }
}