import 'package:flutter/material.dart';
import '../../../../core/widgets/common/custom_text_field.dart';
import '../../../../core/widgets/common/custom_button.dart';
import '../../../../core/constants/dimensions.dart';

/// 认证表单组件
class AuthFormWidget extends StatefulWidget {
  final AuthFormType formType;
  final VoidCallback? onSubmit;
  final VoidCallback? onSwitchForm;
  final bool isLoading;
  final String? errorMessage;
  /// 登录表单提交回调，传递手机号和密码
  final Function(String phone, String password, bool rememberMe)? onLoginSubmit;

  const AuthFormWidget({
    super.key,
    required this.formType,
    this.onSubmit,
    this.onSwitchForm,
    this.isLoading = false,
    this.errorMessage,
    this.onLoginSubmit,
  });

  @override
  State<AuthFormWidget> createState() => _AuthFormWidgetState();
}

class _AuthFormWidgetState extends State<AuthFormWidget> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _usernameController = TextEditingController();
  final _verificationCodeController = TextEditingController();
  final _invitationCodeController = TextEditingController();
  
  bool _rememberMe = false;
  bool _agreedToPolicy = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _usernameController.dispose();
    _verificationCodeController.dispose();
    _invitationCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ..._buildFormFields(),
          const SizedBox(height: AppDimensions.paddingXXL),
          ..._buildFormActions(),
          if (widget.errorMessage != null) ...[
            const SizedBox(height: AppDimensions.paddingL),
            _buildErrorMessage(),
          ],
        ],
      ),
    );
  }

  List<Widget> _buildFormFields() {
    switch (widget.formType) {
      case AuthFormType.login:
        return _buildLoginFields();
      case AuthFormType.register:
        return _buildRegisterFields();
      case AuthFormType.forgotPassword:
        return _buildForgotPasswordFields();
    }
  }

  List<Widget> _buildLoginFields() {
    return [
      CustomTextFieldTypes.phone(
        controller: _phoneController,
        textInputAction: TextInputAction.next,
      ),
      const SizedBox(height: AppDimensions.paddingXL),
      CustomTextFieldTypes.password(
        controller: _passwordController,
        textInputAction: TextInputAction.done,
        onSubmitted: (_) => _handleSubmit(),
      ),
      const SizedBox(height: AppDimensions.paddingL),
      _buildRememberMeRow(),
    ];
  }

  List<Widget> _buildRegisterFields() {
    return [
      CustomTextFieldTypes.username(
        controller: _usernameController,
        textInputAction: TextInputAction.next,
      ),
      const SizedBox(height: AppDimensions.paddingXL),
      CustomTextFieldTypes.phone(
        controller: _phoneController,
        textInputAction: TextInputAction.next,
      ),
      const SizedBox(height: AppDimensions.paddingXL),
      _buildVerificationCodeRow(),
      const SizedBox(height: AppDimensions.paddingXL),
      CustomTextFieldTypes.password(
        labelText: '设置密码',
        hintText: '请设置登录密码',
        controller: _passwordController,
        textInputAction: TextInputAction.next,
      ),
      const SizedBox(height: AppDimensions.paddingXL),
      CustomTextField(
        labelText: '确认密码',
        hintText: '请再次输入密码',
        prefixIcon: const Icon(Icons.lock_outline_rounded),
        obscureText: true,
        controller: _confirmPasswordController,
        textInputAction: TextInputAction.next,
        validator: (value) {
          if (value != _passwordController.text) {
            return '两次输入的密码不一致';
          }
          return null;
        },
      ),
      const SizedBox(height: AppDimensions.paddingXL),
      CustomTextField(
        labelText: '邀请码 (选填)',
        hintText: '请输入邀请码',
        prefixIcon: const Icon(Icons.card_giftcard_rounded),
        controller: _invitationCodeController,
        textInputAction: TextInputAction.done,
      ),
      const SizedBox(height: AppDimensions.paddingXL),
      _buildPolicyAgreementRow(),
    ];
  }

  List<Widget> _buildForgotPasswordFields() {
    return [
      CustomTextFieldTypes.phone(
        controller: _phoneController,
        textInputAction: TextInputAction.next,
      ),
      const SizedBox(height: AppDimensions.paddingXL),
      _buildVerificationCodeRow(),
      const SizedBox(height: AppDimensions.paddingXL),
      CustomTextFieldTypes.password(
        labelText: '新密码',
        hintText: '请输入新密码',
        controller: _passwordController,
        textInputAction: TextInputAction.next,
      ),
      const SizedBox(height: AppDimensions.paddingXL),
      CustomTextField(
        labelText: '确认新密码',
        hintText: '请再次输入新密码',
        prefixIcon: const Icon(Icons.lock_outline_rounded),
        obscureText: true,
        controller: _confirmPasswordController,
        textInputAction: TextInputAction.done,
        validator: (value) {
          if (value != _passwordController.text) {
            return '两次输入的密码不一致';
          }
          return null;
        },
        onSubmitted: (_) => _handleSubmit(),
      ),
    ];
  }

  Widget _buildVerificationCodeRow() {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            flex: 3,
            child: CustomTextFieldTypes.verificationCode(
              controller: _verificationCodeController,
              textInputAction: TextInputAction.next,
            ),
          ),
          const SizedBox(width: AppDimensions.paddingM),
          Expanded(
            flex: 2,
            child: CustomButton(
              text: '获取验证码',
              type: ButtonType.secondary,
              onPressed: _handleSendCode,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRememberMeRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Checkbox(
              value: _rememberMe,
              onChanged: (value) {
                setState(() {
                  _rememberMe = value!;
                });
              },
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            const Text('记住我'),
          ],
        ),
        TextButton(
          onPressed: widget.onSwitchForm,
          child: const Text('忘记密码?'),
        ),
      ],
    );
  }

  Widget _buildPolicyAgreementRow() {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Checkbox(
          value: _agreedToPolicy,
          onChanged: (value) {
            setState(() {
              _agreedToPolicy = value!;
            });
          },
        ),
        Expanded(
          child: RichText(
            text: TextSpan(
              text: '我已阅读并同意 ',
              style: theme.textTheme.bodySmall,
              children: [
                TextSpan(
                  text: '隐私协议',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildFormActions() {
    switch (widget.formType) {
      case AuthFormType.login:
        return [
          CustomButton(
            text: '登 录',
            isLoading: widget.isLoading,
            isExpanded: true,
            onPressed: _handleSubmit,
          ),
        ];
      case AuthFormType.register:
        return [
          CustomButton(
            text: '注 册',
            isLoading: widget.isLoading,
            isExpanded: true,
            onPressed: _canSubmitRegister() ? _handleSubmit : null,
          ),
        ];
      case AuthFormType.forgotPassword:
        return [
          CustomButton(
            text: '确认修改',
            isLoading: widget.isLoading,
            isExpanded: true,
            onPressed: _handleSubmit,
          ),
        ];
    }
  }

  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 20),
          const SizedBox(width: AppDimensions.paddingS),
          Expanded(
            child: Text(
              widget.errorMessage!,
              style: const TextStyle(color: Colors.red, fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  bool _canSubmitRegister() {
    return _agreedToPolicy;
  }

  void _handleSubmit() {
    if (_formKey.currentState?.validate() ?? false) {
      // 如果是登录表单且有登录回调，则调用登录回调
      if (widget.formType == AuthFormType.login && widget.onLoginSubmit != null) {
        widget.onLoginSubmit!(phone, password, rememberMe);
      } else {
        widget.onSubmit?.call();
      }
    }
  }

  void _handleSendCode() {
    if (_phoneController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先输入手机号')),
      );
      return;
    }
    // TODO: 发送验证码逻辑
  }

  // Getters for form data
  String get phone => _phoneController.text;
  String get password => _passwordController.text;
  String get username => _usernameController.text;
  String get verificationCode => _verificationCodeController.text;
  String get invitationCode => _invitationCodeController.text;
  bool get rememberMe => _rememberMe;
}

/// 认证表单类型
enum AuthFormType {
  login,
  register,
  forgotPassword,
}