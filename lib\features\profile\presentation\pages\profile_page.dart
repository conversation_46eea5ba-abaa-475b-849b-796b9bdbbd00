import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/platform/custom_title_bar.dart';
import '../../../../core/constants/dimensions.dart';
import '../../../../shared/utils/responsive_utils.dart';
import '../../../auth/presentation/providers/auth_provider.dart';
import '../../../auth/presentation/pages/login_page.dart';

/// 用户中心页面
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    // 小屏幕需要自己的标题栏，大屏幕由MainShell提供
    if (ResponsiveUtils.isMobile(context)) {
      return const Scaffold(
        appBar: CustomTitleBar(
          title: '八字命理 - 个人中心',
          showBackButton: false,
        ),
        body: _ProfileContent(),
      );
    } else {
      return const _ProfileContent();
    }
  }
}

class _ProfileContent extends StatelessWidget {
  const _ProfileContent();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingXXL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 用户信息区域
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingXXL),
              child: Column(
                children: [
                  // 头像
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                    child: Icon(
                      Icons.person,
                      size: 40,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: AppDimensions.paddingL),
                  // 用户名
                  Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                      final user = authProvider.user;
                      return Text(
                        user?.username ?? '用户',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: AppDimensions.paddingS),
                  // 用户状态
                  Text(
                    '八字命理探索者',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: AppDimensions.paddingXXL),
          
          // 功能选项区域
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: Icon(
                    Icons.settings,
                    color: theme.colorScheme.primary,
                  ),
                  title: const Text('设置'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('设置功能正在开发中')),
                    );
                  },
                ),
                Divider(height: 1, color: theme.dividerColor),
                ListTile(
                  leading: Icon(
                    Icons.help_outline,
                    color: theme.colorScheme.primary,
                  ),
                  title: const Text('帮助与支持'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('帮助功能正在开发中')),
                    );
                  },
                ),
                Divider(height: 1, color: theme.dividerColor),
                ListTile(
                  leading: const Icon(
                    Icons.info_outline,
                    color: Colors.grey,
                  ),
                  title: const Text('关于应用'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showAboutDialog(context);
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppDimensions.paddingXXL),
          
          // 退出登录按钮
          ElevatedButton.icon(
            onPressed: () => _showLogoutDialog(context),
            icon: const Icon(Icons.logout),
            label: const Text('退出登录'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingL),
            ),
          ),
        ],
      ),
    );
  }

  // 显示关于对话框
  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: '八字命理',
      applicationVersion: '1.0.0',
      applicationIcon: Icon(
        Icons.brightness_7_outlined,
        size: 48,
        color: Theme.of(context).colorScheme.primary,
      ),
      children: [
        const Text('基于Flutter开发的八字命理应用'),
        const SizedBox(height: 16),
        const Text('✨ 模块化架构重构完成'),
        const Text('🎯 代码职责清晰分离'),
        const Text('📱 响应式设计适配'),
        const Text('🎨 优雅的UI交互体验'),
      ],
    );
  }

  // 显示退出登录确认对话框
  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认退出'),
          content: const Text('您确定要退出登录吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭对话框
                _logout(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('退出'),
            ),
          ],
        );
      },
    );
  }

  // 执行退出登录
  void _logout(BuildContext context) {
    final authProvider = context.read<AuthProvider>();
    authProvider.logout();
    
    // 清除所有导航栈，返回到登录页面
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const LoginPage()),
      (Route<dynamic> route) => false,
    );
  }
}