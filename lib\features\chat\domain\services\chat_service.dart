import '../../data/models/chat_message.dart';
import '../../data/models/chat_conversation.dart';
import '../../data/models/agent_info.dart';
import '../../data/models/bazi_option.dart';

/// 聊天服务接口
abstract class ChatService {
  /// 发送消息
  Future<ChatMessage> sendMessage({
    required String conversationId,
    required String content,
    required String agentId,
    List<String>? baziIds,
    Map<String, dynamic>? metadata,
  });

  /// 获取AI回复
  Future<ChatMessage> getAiResponse({
    required String conversationId,
    required ChatMessage userMessage,
    required String agentId,
    List<String>? baziIds,
  });

  /// 创建新对话
  Future<ChatConversation> createConversation({
    String? title,
    String? agentId,
    List<String>? baziIds,
  });

  /// 获取对话列表
  Future<List<ChatConversation>> getConversations({
    int limit = 50,
    int offset = 0,
    String? agentId,
  });

  /// 获取单个对话
  Future<ChatConversation?> getConversation(String conversationId);

  /// 更新对话
  Future<ChatConversation> updateConversation(ChatConversation conversation);

  /// 删除对话
  Future<void> deleteConversation(String conversationId);

  /// 删除消息
  Future<void> deleteMessage(String conversationId, String messageId);

  /// 清空对话消息
  Future<void> clearConversation(String conversationId);

  /// 搜索消息
  Future<List<ChatMessage>> searchMessages({
    required String query,
    String? conversationId,
    String? agentId,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 20,
  });

  /// 获取助手列表
  Future<List<AgentInfo>> getAgents();

  /// 获取八字选项列表
  Future<List<BaziOption>> getBaziOptions();

  /// 创建八字选项
  Future<BaziOption> createBaziOption(BaziOption baziOption);

  /// 更新八字选项
  Future<BaziOption> updateBaziOption(BaziOption baziOption);

  /// 删除八字选项
  Future<void> deleteBaziOption(String baziId);

  /// 获取快捷操作列表
  Future<List<String>> getQuickActions({String? agentId});

  /// 导出对话
  Future<String> exportConversation(String conversationId, {ExportFormat format = ExportFormat.text});

  /// 导入对话
  Future<ChatConversation> importConversation(String data, {ExportFormat format = ExportFormat.text});

  /// 获取对话统计信息
  Future<ChatStats> getChatStats({
    String? agentId,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// 设置消息已读
  Future<void> markMessageAsRead(String conversationId, String messageId);

  /// 设置对话置顶
  Future<void> pinConversation(String conversationId, bool isPinned);

  /// 获取历史记录
  Future<List<ChatMessage>> getMessageHistory({
    String? conversationId,
    int limit = 100,
    String? cursor,
  });
}

/// 导出格式枚举
enum ExportFormat {
  text,
  json,
  markdown,
  pdf,
}

/// 聊天统计信息
class ChatStats {
  final int totalConversations;
  final int totalMessages;
  final int userMessages;
  final int aiMessages;
  final Map<String, int> agentUsage;
  final Map<String, int> dailyMessages;
  final double averageResponseTime;
  final DateTime? firstMessageTime;
  final DateTime? lastMessageTime;

  const ChatStats({
    required this.totalConversations,
    required this.totalMessages,
    required this.userMessages,
    required this.aiMessages,
    required this.agentUsage,
    required this.dailyMessages,
    required this.averageResponseTime,
    this.firstMessageTime,
    this.lastMessageTime,
  });

  factory ChatStats.fromJson(Map<String, dynamic> json) {
    return ChatStats(
      totalConversations: json['total_conversations'] as int,
      totalMessages: json['total_messages'] as int,
      userMessages: json['user_messages'] as int,
      aiMessages: json['ai_messages'] as int,
      agentUsage: Map<String, int>.from(json['agent_usage'] as Map),
      dailyMessages: Map<String, int>.from(json['daily_messages'] as Map),
      averageResponseTime: (json['average_response_time'] as num).toDouble(),
      firstMessageTime: json['first_message_time'] != null
          ? DateTime.parse(json['first_message_time'] as String)
          : null,
      lastMessageTime: json['last_message_time'] != null
          ? DateTime.parse(json['last_message_time'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_conversations': totalConversations,
      'total_messages': totalMessages,
      'user_messages': userMessages,
      'ai_messages': aiMessages,
      'agent_usage': agentUsage,
      'daily_messages': dailyMessages,
      'average_response_time': averageResponseTime,
      'first_message_time': firstMessageTime?.toIso8601String(),
      'last_message_time': lastMessageTime?.toIso8601String(),
    };
  }
}