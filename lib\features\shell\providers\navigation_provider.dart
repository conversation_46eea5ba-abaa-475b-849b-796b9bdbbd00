import 'package:flutter/material.dart';

/// 导航状态管理
class NavigationProvider extends ChangeNotifier {
  int _selectedIndex = 0;
  String _currentRoute = '/';
  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      id: 'chat',
      label: 'AI对话',
      icon: Icons.chat_bubble_outline,
      activeIcon: Icons.chat_bubble,
      route: '/chat',
    ),
    NavigationItem(
      id: 'bazi',
      label: '排盘',
      icon: Icons.grid_view_outlined,
      activeIcon: Icons.grid_view_sharp,
      route: '/bazi',
    ),
    NavigationItem(
      id: 'profile',
      label: '我的',
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      route: '/profile',
    ),
  ];

  // Getters
  int get selectedIndex => _selectedIndex;
  String get currentRoute => _currentRoute;
  List<NavigationItem> get navigationItems => _navigationItems;
  NavigationItem get currentItem => _navigationItems[_selectedIndex];

  /// 选择导航项
  void selectIndex(int index) {
    if (index >= 0 && index < _navigationItems.length && index != _selectedIndex) {
      _selectedIndex = index;
      _currentRoute = _navigationItems[index].route;
      notifyListeners();
    }
  }

  /// 根据路由选择导航项
  void selectByRoute(String route) {
    final index = _navigationItems.indexWhere((item) => item.route == route);
    if (index != -1) {
      selectIndex(index);
    }
  }

  /// 添加导航项
  void addNavigationItem(NavigationItem item) {
    _navigationItems.add(item);
    notifyListeners();
  }

  /// 移除导航项
  void removeNavigationItem(String id) {
    _navigationItems.removeWhere((item) => item.id == id);
    
    // 如果移除的是当前选中的项，重置到第一项
    if (_selectedIndex >= _navigationItems.length) {
      _selectedIndex = 0;
      _currentRoute = _navigationItems.isNotEmpty ? _navigationItems[0].route : '/';
    }
    
    notifyListeners();
  }

  /// 更新导航项
  void updateNavigationItem(String id, NavigationItem newItem) {
    final index = _navigationItems.indexWhere((item) => item.id == id);
    if (index != -1) {
      _navigationItems[index] = newItem;
      notifyListeners();
    }
  }

  /// 设置导航项角标
  void setBadge(String id, String? badge) {
    final index = _navigationItems.indexWhere((item) => item.id == id);
    if (index != -1) {
      _navigationItems[index] = _navigationItems[index].copyWith(badge: badge);
      notifyListeners();
    }
  }

  /// 清除所有角标
  void clearAllBadges() {
    for (int i = 0; i < _navigationItems.length; i++) {
      if (_navigationItems[i].badge != null) {
        _navigationItems[i] = _navigationItems[i].copyWith(badge: null);
      }
    }
    notifyListeners();
  }

  /// 检查是否可以返回上一页
  bool canPop() {
    // 这里可以实现更复杂的导航栈逻辑
    return _selectedIndex != 0;
  }

  /// 返回到首页
  void goHome() {
    selectIndex(0);
  }

  /// 重置导航状态
  void reset() {
    _selectedIndex = 0;
    _currentRoute = _navigationItems.isNotEmpty ? _navigationItems[0].route : '/';
    clearAllBadges();
    notifyListeners();
  }
}

/// 导航项模型
class NavigationItem {
  final String id;
  final String label;
  final IconData icon;
  final IconData activeIcon;
  final String route;
  final String? badge;
  final bool isEnabled;
  final VoidCallback? onTap;

  const NavigationItem({
    required this.id,
    required this.label,
    required this.icon,
    required this.activeIcon,
    required this.route,
    this.badge,
    this.isEnabled = true,
    this.onTap,
  });

  /// 创建副本
  NavigationItem copyWith({
    String? id,
    String? label,
    IconData? icon,
    IconData? activeIcon,
    String? route,
    String? badge,
    bool? isEnabled,
    VoidCallback? onTap,
  }) {
    return NavigationItem(
      id: id ?? this.id,
      label: label ?? this.label,
      icon: icon ?? this.icon,
      activeIcon: activeIcon ?? this.activeIcon,
      route: route ?? this.route,
      badge: badge ?? this.badge,
      isEnabled: isEnabled ?? this.isEnabled,
      onTap: onTap ?? this.onTap,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NavigationItem &&
        other.id == id &&
        other.label == label &&
        other.route == route &&
        other.badge == badge &&
        other.isEnabled == isEnabled;
  }

  @override
  int get hashCode {
    return Object.hash(id, label, route, badge, isEnabled);
  }

  @override
  String toString() {
    return 'NavigationItem(id: $id, label: $label, route: $route, badge: $badge, isEnabled: $isEnabled)';
  }
}