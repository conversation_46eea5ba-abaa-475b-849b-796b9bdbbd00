import 'package:flutter/material.dart';
import '../../../../core/widgets/platform/custom_title_bar.dart';
import '../../../../shared/utils/responsive_utils.dart';

/// 八字排盘页面
class BaziChartPage extends StatelessWidget {
  const BaziChartPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 小屏幕需要自己的标题栏，大屏幕由MainShell提供
    if (ResponsiveUtils.isMobile(context)) {
      return const Scaffold(
        appBar: CustomTitleBar(
          title: '八字命理 - 排盘推算',
          showBackButton: false,
        ),
        body: _BaziContent(),
      );
    } else {
      return const _BaziContent();
    }
  }
}

class _BaziContent extends StatelessWidget {
  const _BaziContent();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      color: theme.colorScheme.background,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.grid_view_sharp,
              size: 64,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              '八字排盘功能',
              style: theme.textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              '模块化架构重构完成\n排盘功能正在开发中...',
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}