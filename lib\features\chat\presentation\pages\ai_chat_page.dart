import 'package:flutter/material.dart';
import '../../../../core/widgets/platform/custom_title_bar.dart';
import '../../../../shared/utils/responsive_utils.dart';

/// AI聊天页面
class AiChatPage extends StatelessWidget {
  const AiChatPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 小屏幕需要自己的标题栏，大屏幕由MainShell提供
    if (ResponsiveUtils.isMobile(context)) {
      return const Scaffold(
        appBar: CustomTitleBar(
          title: '八字命理 - AI智能问答',
          showBackButton: false,
        ),
        body: _ChatContent(),
      );
    } else {
      return const _ChatContent();
    }
  }
}

class _ChatContent extends StatelessWidget {
  const _ChatContent();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      color: theme.colorScheme.background,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.auto_awesome,
              size: 64,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'AI聊天功能',
              style: theme.textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              '模块化架构重构完成\n聊天功能正在开发中...',
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}