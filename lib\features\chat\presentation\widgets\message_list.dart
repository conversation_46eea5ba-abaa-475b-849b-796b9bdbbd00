import 'package:flutter/material.dart';
import '../../../../core/constants/dimensions.dart';
import '../../data/models/chat_message.dart';
import 'message_bubble.dart';
import 'typing_indicator.dart';

/// 消息列表组件
class MessageList extends StatefulWidget {
  final List<ChatMessage> messages;
  final bool isTyping;
  final Function(ChatMessage)? onMessageTap;
  final Function(ChatMessage)? onMessageLongPress;

  const MessageList({
    super.key,
    required this.messages,
    this.isTyping = false,
    this.onMessageTap,
    this.onMessageLongPress,
  });

  @override
  State<MessageList> createState() => _MessageListState();
}

class _MessageListState extends State<MessageList> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  @override
  void didUpdateWidget(MessageList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.messages.length != oldWidget.messages.length || 
        widget.isTyping != oldWidget.isTyping) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      itemCount: widget.messages.length + (widget.isTyping ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == widget.messages.length) {
          // 显示正在输入指示器
          return const Padding(
            padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
            child: TypingIndicator(agentName: '智能助手'),
          );
        }

        final message = widget.messages[index];
        final isFirstMessage = index == 0;
        final isLastMessage = index == widget.messages.length - 1;
        final previousMessage = index > 0 ? widget.messages[index - 1] : null;
        final nextMessage = index < widget.messages.length - 1 ? widget.messages[index + 1] : null;

        // 判断是否显示时间戳
        bool showTimestamp = isFirstMessage || 
            (previousMessage != null && 
             message.timestamp.difference(previousMessage.timestamp).inMinutes > 5);

        // 判断是否显示头像
        bool showAvatar = isLastMessage || 
            (nextMessage != null && nextMessage.isUser != message.isUser);

        return Column(
          children: [
            if (showTimestamp) _buildTimestamp(context, message.timestamp),
            Padding(
              padding: EdgeInsets.only(
                bottom: showAvatar ? AppDimensions.paddingL : AppDimensions.paddingS,
              ),
              child: MessageBubble(
                message: message,
                showAvatar: showAvatar,
                showTimestamp: false,
                onTap: widget.onMessageTap != null 
                    ? () => widget.onMessageTap!(message)
                    : null,
                onLongPress: widget.onMessageLongPress != null
                    ? () => widget.onMessageLongPress!(message)
                    : null,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTimestamp(BuildContext context, DateTime timestamp) {
    final theme = Theme.of(context);
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    String timeText;
    if (difference.inDays == 0) {
      timeText = '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      timeText = '昨天 ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays < 7) {
      const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      timeText = '${weekdays[timestamp.weekday - 1]} ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else {
      timeText = '${timestamp.month}月${timestamp.day}日 ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingL),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingM,
            vertical: AppDimensions.paddingS,
          ),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceVariant.withOpacity(0.7),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          child: Text(
            timeText,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      ),
    );
  }
}