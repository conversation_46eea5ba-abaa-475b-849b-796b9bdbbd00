import 'dart:io';
import 'package:flutter/material.dart';
import 'package:window_manager/window_manager.dart';

/// 窗口拖拽区域组件
class WindowDragArea extends StatelessWidget {
  final Widget child;
  final bool enableDrag;
  final bool enableDoubleClickToggle;
  
  const WindowDragArea({
    super.key,
    required this.child,
    this.enableDrag = true,
    this.enableDoubleClickToggle = true,
  });

  @override
  Widget build(BuildContext context) {
    // 只在桌面平台启用拖拽功能
    if (!Platform.isWindows && !Platform.isLinux && !Platform.isMacOS) {
      return child;
    }
    
    return GestureDetector(
      onPanStart: enableDrag ? (details) {
        windowManager.startDragging();
      } : null,
      onDoubleTap: enableDoubleClickToggle ? () async {
        bool isMaximized = await windowManager.isMaximized();
        if (isMaximized) {
          windowManager.unmaximize();
        } else {
          windowManager.maximize();
        }
      } : null,
      child: child,
    );
  }
}