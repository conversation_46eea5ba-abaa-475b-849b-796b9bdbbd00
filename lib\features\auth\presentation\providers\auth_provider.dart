import 'package:flutter/material.dart';
import '../../domain/services/auth_service.dart';
import '../../data/models/user_model.dart';
import '../../data/repositories/auth_repository.dart';

/// 认证状态管理
class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthRepository();
  
  UserModel? _user;
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;

  // Getters
  UserModel? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _user != null;
  bool get isInitialized => _isInitialized;

  /// 初始化认证状态
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    _setLoading(true);
    try {
      _user = await _authService.getCurrentUser();
      _isInitialized = true;
      _clearError();
    } catch (e) {
      _setError('初始化失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 登录
  Future<bool> login({
    required String phone,
    required String password,
    bool rememberMe = false,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      _user = await _authService.login(
        phone: phone,
        password: password,
        rememberMe: rememberMe,
      );
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 注册
  Future<bool> register({
    required String username,
    required String phone,
    required String password,
    required String verificationCode,
    String? invitationCode,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      _user = await _authService.register(
        username: username,
        phone: phone,
        password: password,
        verificationCode: verificationCode,
        invitationCode: invitationCode,
      );
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 发送验证码
  Future<bool> sendVerificationCode({
    required String phone,
    required VerificationCodeType type,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      await _authService.sendVerificationCode(
        phone: phone,
        type: type,
      );
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 重置密码
  Future<bool> resetPassword({
    required String phone,
    required String newPassword,
    required String verificationCode,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      await _authService.resetPassword(
        phone: phone,
        newPassword: newPassword,
        verificationCode: verificationCode,
      );
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 登出
  Future<void> logout() async {
    _setLoading(true);
    try {
      await _authService.logout();
      _user = null;
      notifyListeners();
    } catch (e) {
      _setError('登出失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 刷新用户信息
  Future<void> refreshUserInfo() async {
    if (!isLoggedIn) return;
    
    _setLoading(true);
    try {
      _user = await _authService.refreshUserInfo();
      notifyListeners();
    } catch (e) {
      _setError('刷新用户信息失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 更新用户信息
  Future<bool> updateUserInfo({
    String? username,
    String? email,
    String? avatar,
  }) async {
    if (!isLoggedIn) return false;
    
    _setLoading(true);
    _clearError();
    
    try {
      _user = await _authService.updateUserInfo(
        username: username,
        email: email,
        avatar: avatar,
      );
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 修改密码
  Future<bool> changePassword({
    required String oldPassword,
    required String newPassword,
  }) async {
    if (!isLoggedIn) return false;
    
    _setLoading(true);
    _clearError();
    
    try {
      await _authService.changePassword(
        oldPassword: oldPassword,
        newPassword: newPassword,
      );
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 检查手机号是否已注册
  Future<bool> isPhoneRegistered(String phone) async {
    try {
      return await _authService.isPhoneRegistered(phone);
    } catch (e) {
      _setError('检查手机号失败: $e');
      return false;
    }
  }

  /// 验证验证码
  Future<bool> verifyCode({
    required String phone,
    required String code,
    required VerificationCodeType type,
  }) async {
    try {
      return await _authService.verifyCode(
        phone: phone,
        code: code,
        type: type,
      );
    } catch (e) {
      _setError('验证码验证失败: $e');
      return false;
    }
  }

  /// 清除错误信息
  void clearError() {
    _clearError();
  }

  // 私有方法
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }
}