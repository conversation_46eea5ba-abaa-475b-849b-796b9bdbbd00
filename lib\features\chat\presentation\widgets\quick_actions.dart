import 'package:flutter/material.dart';
import '../../../../core/constants/dimensions.dart';

/// 快捷操作组件
class QuickActions extends StatelessWidget {
  final List<String> quickActions;
  final Function(String) onQuickActionTap;

  const QuickActions({
    super.key,
    required this.quickActions,
    required this.onQuickActionTap,
  });

  @override
  Widget build(BuildContext context) {
    if (quickActions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '快捷操作',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Wrap(
            spacing: AppDimensions.paddingM,
            runSpacing: AppDimensions.paddingM,
            children: quickActions.map((action) => _buildQuickActionChip(context, action)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionChip(BuildContext context, String action) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: () => onQuickActionTap(action),
      borderRadius: BorderRadius.circular(AppDimensions.radiusL),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingL,
          vertical: AppDimensions.paddingM,
        ),
        decoration: BoxDecoration(
          color: theme.colorScheme.primaryContainer.withOpacity(0.3),
          border: Border.all(
            color: theme.colorScheme.primary.withOpacity(0.3),
          ),
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.touch_app,
              size: 16,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: AppDimensions.paddingS),
            Text(
              action,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}