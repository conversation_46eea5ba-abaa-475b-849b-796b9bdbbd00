/// 聊天消息模型
class ChatMessage {
  final String id;
  final String content;
  final bool isUser;
  final DateTime timestamp;
  final String agentName;
  final MessageStatus status;
  final Map<String, dynamic>? metadata;

  const ChatMessage({
    required this.id,
    required this.content,
    required this.isUser,
    required this.timestamp,
    required this.agentName,
    this.status = MessageStatus.sent,
    this.metadata,
  });

  /// 从JSON创建消息
  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] as String,
      content: json['content'] as String,
      isUser: json['is_user'] as bool,
      timestamp: DateTime.parse(json['timestamp'] as String),
      agentName: json['agent_name'] as String,
      status: MessageStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => MessageStatus.sent,
      ),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'is_user': isUser,
      'timestamp': timestamp.toIso8601String(),
      'agent_name': agentName,
      'status': status.name,
      'metadata': metadata,
    };
  }

  /// 创建副本
  ChatMessage copyWith({
    String? id,
    String? content,
    bool? isUser,
    DateTime? timestamp,
    String? agentName,
    MessageStatus? status,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      isUser: isUser ?? this.isUser,
      timestamp: timestamp ?? this.timestamp,
      agentName: agentName ?? this.agentName,
      status: status ?? this.status,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 创建用户消息
  factory ChatMessage.user({
    required String content,
    required String agentName,
    String? id,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      isUser: true,
      timestamp: timestamp ?? DateTime.now(),
      agentName: agentName,
      status: MessageStatus.sent,
      metadata: metadata,
    );
  }

  /// 创建AI消息
  factory ChatMessage.ai({
    required String content,
    required String agentName,
    String? id,
    DateTime? timestamp,
    MessageStatus status = MessageStatus.sent,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      isUser: false,
      timestamp: timestamp ?? DateTime.now(),
      agentName: agentName,
      status: status,
      metadata: metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatMessage &&
        other.id == id &&
        other.content == content &&
        other.isUser == isUser &&
        other.timestamp == timestamp &&
        other.agentName == agentName &&
        other.status == status;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      content,
      isUser,
      timestamp,
      agentName,
      status,
    );
  }

  @override
  String toString() {
    return 'ChatMessage(id: $id, content: $content, isUser: $isUser, timestamp: $timestamp, agentName: $agentName, status: $status, metadata: $metadata)';
  }
}

/// 消息状态枚举
enum MessageStatus {
  sending,    // 发送中
  sent,       // 已发送
  delivered,  // 已送达
  failed,     // 发送失败
}