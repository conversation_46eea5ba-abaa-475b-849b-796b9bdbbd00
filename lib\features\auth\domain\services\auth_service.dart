import '../../data/models/user_model.dart';

/// 认证服务接口
abstract class AuthService {
  /// 登录
  Future<UserModel> login({
    required String phone,
    required String password,
    bool rememberMe = false,
  });

  /// 注册
  Future<UserModel> register({
    required String username,
    required String phone,
    required String password,
    required String verificationCode,
    String? invitationCode,
  });

  /// 发送验证码
  Future<void> sendVerificationCode({
    required String phone,
    required VerificationCodeType type,
  });

  /// 重置密码
  Future<void> resetPassword({
    required String phone,
    required String newPassword,
    required String verificationCode,
  });

  /// 登出
  Future<void> logout();

  /// 获取当前用户信息
  Future<UserModel?> getCurrentUser();

  /// 刷新用户信息
  Future<UserModel> refreshUserInfo();

  /// 检查手机号是否已注册
  Future<bool> isPhoneRegistered(String phone);

  /// 验证验证码
  Future<bool> verifyCode({
    required String phone,
    required String code,
    required VerificationCodeType type,
  });

  /// 更新用户信息
  Future<UserModel> updateUserInfo({
    String? username,
    String? email,
    String? avatar,
  });

  /// 修改密码
  Future<void> changePassword({
    required String oldPassword,
    required String newPassword,
  });

  /// 检查登录状态
  Future<bool> isLoggedIn();

  /// 获取存储的用户信息
  Future<UserModel?> getStoredUser();

  /// 清除用户数据
  Future<void> clearUserData();
}

/// 验证码类型枚举
enum VerificationCodeType {
  register,    // 注册
  login,       // 登录
  resetPassword, // 重置密码
  changePhone,   // 更换手机号
}