/// 应用路由定义
class AppRoutes {
  AppRoutes._();

  // 认证相关路由
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  
  // 主要页面路由
  static const String home = '/';
  static const String chat = '/chat';
  static const String bazi = '/bazi';
  static const String profile = '/profile';
  
  // 设置相关路由
  static const String settings = '/settings';
  static const String about = '/about';
  static const String help = '/help';
  static const String privacy = '/privacy';
  
  // 八字相关路由
  static const String baziDetail = '/bazi-detail';
  static const String baziCreate = '/bazi-create';
  static const String baziEdit = '/bazi-edit';
  
  // 聊天相关路由
  static const String chatDetail = '/chat-detail';
  static const String chatSearch = '/chat-search';
  static const String chatExport = '/chat-export';
  
  /// 获取所有路由列表
  static List<String> getAllRoutes() {
    return [
      login,
      register,
      forgotPassword,
      home,
      chat,
      bazi,
      profile,
      settings,
      about,
      help,
      privacy,
      baziDetail,
      baziCreate,
      baziEdit,
      chatDetail,
      chatSearch,
      chatExport,
    ];
  }

  /// 判断是否为认证路由
  static bool isAuthRoute(String route) {
    return [login, register, forgotPassword].contains(route);
  }

  /// 判断是否为受保护的路由
  static bool isProtectedRoute(String route) {
    return !isAuthRoute(route) && route != privacy;
  }
}