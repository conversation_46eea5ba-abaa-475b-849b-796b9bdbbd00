import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../core/constants/colors.dart';
import 'theme_constants.dart';

/// 应用主题定义
class AppTheme {
  AppTheme._();

  /// 亮色主题
  static final ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    primaryColor: AppColors.lightPrimaryText,
    colorScheme: ThemeConstants.lightColorScheme,
    scaffoldBackgroundColor: AppColors.lightBackgroundColor,
    
    // AppBar主题
    appBarTheme: _buildAppBarTheme(isLight: true),
    
    // 文本主题
    textTheme: _buildTextTheme(isLight: true),
    
    // 输入框主题
    inputDecorationTheme: _buildInputDecorationTheme(isLight: true),
    
    // 按钮主题
    elevatedButtonTheme: _buildElevatedButtonTheme(),
    textButtonTheme: _buildTextButtonTheme(isLight: true),
    
    // 卡片主题
    cardTheme: _buildCardTheme(isLight: true),
    
    // 复选框主题
    checkboxTheme: _buildCheckboxTheme(),
    
    // 底部导航栏主题
    bottomNavigationBarTheme: _buildBottomNavigationBarTheme(isLight: true),
    
    // 对话框主题
    dialogTheme: _buildDialogTheme(isLight: true),
    
    // 分割线主题
    dividerTheme: _buildDividerTheme(isLight: true),
    
    // 浮动操作按钮主题
    floatingActionButtonTheme: _buildFloatingActionButtonTheme(),
    
    // 图标主题
    iconTheme: _buildIconTheme(isLight: true),
    
    // 列表瓦片主题
    listTileTheme: _buildListTileTheme(isLight: true),
  );

  /// 暗色主题
  static final ThemeData darkTheme = ThemeData(
    brightness: Brightness.dark,
    primaryColor: AppColors.darkPrimaryText,
    colorScheme: ThemeConstants.darkColorScheme,
    scaffoldBackgroundColor: AppColors.darkBackgroundColor,
    
    // AppBar主题
    appBarTheme: _buildAppBarTheme(isLight: false),
    
    // 文本主题
    textTheme: _buildTextTheme(isLight: false),
    
    // 输入框主题
    inputDecorationTheme: _buildInputDecorationTheme(isLight: false),
    
    // 按钮主题
    elevatedButtonTheme: _buildElevatedButtonTheme(),
    textButtonTheme: _buildTextButtonTheme(isLight: false),
    
    // 卡片主题
    cardTheme: _buildCardTheme(isLight: false),
    
    // 复选框主题
    checkboxTheme: _buildCheckboxTheme(),
    
    // 底部导航栏主题
    bottomNavigationBarTheme: _buildBottomNavigationBarTheme(isLight: false),
    
    // 对话框主题
    dialogTheme: _buildDialogTheme(isLight: false),
    
    // 分割线主题
    dividerTheme: _buildDividerTheme(isLight: false),
    
    // 浮动操作按钮主题
    floatingActionButtonTheme: _buildFloatingActionButtonTheme(),
    
    // 图标主题
    iconTheme: _buildIconTheme(isLight: false),
    
    // 列表瓦片主题
    listTileTheme: _buildListTileTheme(isLight: false),
  );

  // AppBar主题构建
  static AppBarTheme _buildAppBarTheme({required bool isLight}) {
    return AppBarTheme(
      color: isLight ? AppColors.lightBackgroundColor : AppColors.darkBackgroundColor,
      elevation: 0,
      iconTheme: IconThemeData(
        color: isLight ? AppColors.lightPrimaryText : AppColors.darkPrimaryText,
      ),
      titleTextStyle: GoogleFonts.notoSansSc(
        color: isLight ? AppColors.lightPrimaryText : AppColors.darkPrimaryText,
        fontSize: ThemeConstants.fontSizeXXL,
        fontWeight: ThemeConstants.fontWeightBold,
      ),
      centerTitle: true,
      systemOverlayStyle: isLight ? 
        SystemUiOverlayStyle.dark : 
        SystemUiOverlayStyle.light,
    );
  }

  // 文本主题构建
  static TextTheme _buildTextTheme({required bool isLight}) {
    final baseColor = isLight ? AppColors.lightPrimaryText : AppColors.darkPrimaryText;
    final secondaryColor = isLight ? AppColors.lightSecondaryText : AppColors.darkSecondaryText;
    
    return GoogleFonts.notoSansScTextTheme(
      TextTheme(
        displayLarge: TextStyle(
          fontSize: ThemeConstants.fontSizeDisplay,
          fontWeight: ThemeConstants.fontWeightBold,
          color: baseColor,
        ),
        displayMedium: TextStyle(
          fontSize: ThemeConstants.fontSizeTitle,
          fontWeight: ThemeConstants.fontWeightBold,
          color: baseColor,
        ),
        displaySmall: TextStyle(
          fontSize: ThemeConstants.fontSizeXXXL,
          fontWeight: ThemeConstants.fontWeightSemiBold,
          color: baseColor,
        ),
        headlineLarge: TextStyle(
          fontSize: ThemeConstants.fontSizeXXL,
          fontWeight: ThemeConstants.fontWeightSemiBold,
          color: baseColor,
        ),
        headlineMedium: TextStyle(
          fontSize: ThemeConstants.fontSizeXL,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: baseColor,
        ),
        headlineSmall: TextStyle(
          fontSize: ThemeConstants.fontSizeL,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: baseColor,
        ),
        titleLarge: TextStyle(
          fontSize: ThemeConstants.fontSizeL,
          fontWeight: ThemeConstants.fontWeightSemiBold,
          color: baseColor,
        ),
        titleMedium: TextStyle(
          fontSize: ThemeConstants.fontSizeM,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: baseColor,
        ),
        titleSmall: TextStyle(
          fontSize: ThemeConstants.fontSizeS,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: baseColor,
        ),
        bodyLarge: TextStyle(
          fontSize: ThemeConstants.fontSizeL,
          fontWeight: ThemeConstants.fontWeightRegular,
          color: baseColor,
        ),
        bodyMedium: TextStyle(
          fontSize: ThemeConstants.fontSizeM,
          fontWeight: ThemeConstants.fontWeightRegular,
          color: secondaryColor,
        ),
        bodySmall: TextStyle(
          fontSize: ThemeConstants.fontSizeS,
          fontWeight: ThemeConstants.fontWeightRegular,
          color: secondaryColor,
        ),
        labelLarge: TextStyle(
          fontSize: ThemeConstants.fontSizeM,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: baseColor,
        ),
        labelMedium: TextStyle(
          fontSize: ThemeConstants.fontSizeS,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: baseColor,
        ),
        labelSmall: TextStyle(
          fontSize: ThemeConstants.fontSizeXS,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: secondaryColor,
        ),
      ),
    );
  }

  // 输入框主题构建
  static InputDecorationTheme _buildInputDecorationTheme({required bool isLight}) {
    return InputDecorationTheme(
      filled: true,
      fillColor: isLight ? AppColors.lightSurfaceColor : AppColors.darkSurfaceColor,
      contentPadding: const EdgeInsets.symmetric(vertical: 15.0, horizontal: 20.0),
      border: OutlineInputBorder(
        borderRadius: ThemeConstants.largeBorderRadius,
        borderSide: isLight ? ThemeConstants.lightBorderSide : ThemeConstants.darkBorderSide,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: ThemeConstants.largeBorderRadius,
        borderSide: isLight ? ThemeConstants.lightBorderSide : ThemeConstants.darkBorderSide,
      ),
      focusedBorder: const OutlineInputBorder(
        borderRadius: ThemeConstants.largeBorderRadius,
        borderSide: ThemeConstants.focusedBorderSide,
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: ThemeConstants.largeBorderRadius,
        borderSide: const BorderSide(color: AppColors.errorColor, width: 1.0),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: ThemeConstants.largeBorderRadius,
        borderSide: const BorderSide(color: AppColors.errorColor, width: 1.5),
      ),
      prefixIconColor: AppColors.lightAccentColor,
      suffixIconColor: isLight ? AppColors.lightSecondaryText : AppColors.darkSecondaryText,
      hintStyle: TextStyle(
        color: isLight ? AppColors.lightSecondaryText : AppColors.darkSecondaryText,
        fontSize: ThemeConstants.fontSizeM,
      ),
      labelStyle: TextStyle(
        color: isLight ? AppColors.lightSecondaryText : AppColors.darkSecondaryText,
        fontSize: ThemeConstants.fontSizeM,
      ),
    );
  }

  // 其他主题构建方法...
  static ElevatedButtonThemeData _buildElevatedButtonTheme() {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        foregroundColor: Colors.white,
        backgroundColor: AppColors.lightAccentColor,
        shape: const RoundedRectangleBorder(
          borderRadius: ThemeConstants.largeBorderRadius,
        ),
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 24.0),
        elevation: 2,
        shadowColor: AppColors.lightAccentColor.withOpacity(0.4),
        textStyle: GoogleFonts.notoSansSc(
          fontSize: ThemeConstants.fontSizeL,
          fontWeight: ThemeConstants.fontWeightBold,
        ),
      ),
    );
  }

  static TextButtonThemeData _buildTextButtonTheme({required bool isLight}) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: isLight ? AppColors.lightPrimaryText : AppColors.darkPrimaryText,
        textStyle: GoogleFonts.notoSansSc(
          fontSize: ThemeConstants.fontSizeM,
          fontWeight: ThemeConstants.fontWeightRegular,
        ),
        shape: const RoundedRectangleBorder(
          borderRadius: ThemeConstants.mediumBorderRadius,
        ),
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
      ),
    );
  }

  static CardThemeData _buildCardTheme({required bool isLight}) {
    return CardThemeData(
      elevation: 1.5,
      color: isLight ? AppColors.lightSurfaceColor : AppColors.darkSurfaceColor,
      shape: const RoundedRectangleBorder(
        borderRadius: ThemeConstants.extraLargeBorderRadius,
      ),
      shadowColor: Colors.black.withOpacity(isLight ? 0.08 : 0.3),
      margin: const EdgeInsets.all(8.0),
    );
  }

  static CheckboxThemeData _buildCheckboxTheme() {
    return CheckboxThemeData(
      shape: const RoundedRectangleBorder(
        borderRadius: ThemeConstants.smallBorderRadius,
      ),
      fillColor: MaterialStateProperty.resolveWith<Color?>((Set<MaterialState> states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.lightAccentColor;
        }
        return null;
      }),
    );
  }

  static BottomNavigationBarThemeData _buildBottomNavigationBarTheme({required bool isLight}) {
    return BottomNavigationBarThemeData(
      backgroundColor: isLight ? AppColors.lightSurfaceColor : AppColors.darkSurfaceColor,
      selectedItemColor: AppColors.lightAccentColor,
      unselectedItemColor: isLight ? AppColors.lightSecondaryText : AppColors.darkSecondaryText,
      elevation: 8,
      type: BottomNavigationBarType.fixed,
      selectedLabelStyle: GoogleFonts.notoSansSc(
        fontSize: ThemeConstants.fontSizeS,
        fontWeight: ThemeConstants.fontWeightMedium,
      ),
      unselectedLabelStyle: GoogleFonts.notoSansSc(
        fontSize: ThemeConstants.fontSizeS,
        fontWeight: ThemeConstants.fontWeightRegular,
      ),
    );
  }

  static DialogTheme _buildDialogTheme({required bool isLight}) {
    return DialogTheme(
      backgroundColor: isLight ? AppColors.lightSurfaceColor : AppColors.darkSurfaceColor,
      shape: const RoundedRectangleBorder(
        borderRadius: ThemeConstants.extraLargeBorderRadius,
      ),
      elevation: 8,
      titleTextStyle: GoogleFonts.notoSansSc(
        fontSize: ThemeConstants.fontSizeXL,
        fontWeight: ThemeConstants.fontWeightBold,
        color: isLight ? AppColors.lightPrimaryText : AppColors.darkPrimaryText,
      ),
      contentTextStyle: GoogleFonts.notoSansSc(
        fontSize: ThemeConstants.fontSizeM,
        fontWeight: ThemeConstants.fontWeightRegular,
        color: isLight ? AppColors.lightSecondaryText : AppColors.darkSecondaryText,
      ),
    );
  }

  static DividerThemeData _buildDividerTheme({required bool isLight}) {
    return DividerThemeData(
      color: isLight ? AppColors.lightBorderColor : AppColors.darkBorderColor,
      thickness: 1,
      space: 1,
    );
  }

  static FloatingActionButtonThemeData _buildFloatingActionButtonTheme() {
    return const FloatingActionButtonThemeData(
      backgroundColor: AppColors.lightAccentColor,
      foregroundColor: Colors.white,
      elevation: 6,
      focusElevation: 8,
      hoverElevation: 8,
      highlightElevation: 12,
      shape: CircleBorder(),
    );
  }

  static IconThemeData _buildIconTheme({required bool isLight}) {
    return IconThemeData(
      color: isLight ? AppColors.lightSecondaryText : AppColors.darkSecondaryText,
      size: 24,
    );
  }

  static ListTileThemeData _buildListTileTheme({required bool isLight}) {
    return ListTileThemeData(
      iconColor: isLight ? AppColors.lightAccentColor : AppColors.darkAccentColor,
      textColor: isLight ? AppColors.lightPrimaryText : AppColors.darkPrimaryText,
      tileColor: Colors.transparent,
      selectedTileColor: AppColors.lightAccentColor.withOpacity(0.1),
      shape: const RoundedRectangleBorder(
        borderRadius: ThemeConstants.mediumBorderRadius,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }
}