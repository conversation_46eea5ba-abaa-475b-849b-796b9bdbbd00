/// 验证器工具类
class Validators {
  Validators._();

  /// 手机号验证
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入手机号';
    }
    if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
      return '请输入有效的手机号';
    }
    return null;
  }

  /// 密码验证
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入密码';
    }
    if (value.length < 6) {
      return '密码长度至少6位';
    }
    if (value.length > 20) {
      return '密码长度不能超过20位';
    }
    return null;
  }

  /// 确认密码验证
  static String? validateConfirmPassword(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return '请确认密码';
    }
    if (value != password) {
      return '两次输入的密码不一致';
    }
    return null;
  }

  /// 用户名验证
  static String? validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入用户名';
    }
    if (value.length < 2) {
      return '用户名至少2个字符';
    }
    if (value.length > 20) {
      return '用户名不能超过20个字符';
    }
    if (!RegExp(r'^[a-zA-Z0-9\u4e00-\u9fa5_]+$').hasMatch(value)) {
      return '用户名只能包含字母、数字、中文和下划线';
    }
    return null;
  }

  /// 验证码验证
  static String? validateVerificationCode(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入验证码';
    }
    if (value.length != 6) {
      return '验证码为6位数字';
    }
    if (!RegExp(r'^\d{6}$').hasMatch(value)) {
      return '验证码只能是数字';
    }
    return null;
  }

  /// 邮箱验证
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入邮箱';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return '请输入有效的邮箱地址';
    }
    return null;
  }

  /// 非空验证
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '请输入$fieldName';
    }
    return null;
  }

  /// 长度验证
  static String? validateLength(
    String? value, 
    int minLength, 
    int maxLength, 
    String fieldName
  ) {
    if (value == null || value.isEmpty) {
      return '请输入$fieldName';
    }
    if (value.length < minLength) {
      return '$fieldName至少$minLength个字符';
    }
    if (value.length > maxLength) {
      return '$fieldName不能超过$maxLength个字符';
    }
    return null;
  }

  /// 数字验证
  static String? validateNumber(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '请输入$fieldName';
    }
    if (double.tryParse(value) == null) {
      return '$fieldName必须是数字';
    }
    return null;
  }

  /// 整数验证
  static String? validateInteger(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '请输入$fieldName';
    }
    if (int.tryParse(value) == null) {
      return '$fieldName必须是整数';
    }
    return null;
  }

  /// 范围验证
  static String? validateRange(
    String? value, 
    double min, 
    double max, 
    String fieldName
  ) {
    final numberValidation = validateNumber(value, fieldName);
    if (numberValidation != null) return numberValidation;
    
    final number = double.parse(value!);
    if (number < min || number > max) {
      return '$fieldName必须在$min-$max之间';
    }
    return null;
  }
}