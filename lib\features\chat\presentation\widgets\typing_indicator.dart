import 'package:flutter/material.dart';
import '../../../../core/constants/dimensions.dart';

/// 输入指示器组件
class TypingIndicator extends StatefulWidget {
  final String agentName;

  const TypingIndicator({
    super.key,
    required this.agentName,
  });

  @override
  State<TypingIndicator> createState() => _TypingIndicatorState();
}

class _TypingIndicatorState extends State<TypingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<AnimationController> _dotControllers;
  late List<Animation<double>> _dotAnimations;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    // 创建三个点的动画控制器
    _dotControllers = List.generate(3, (index) {
      return AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      );
    });
    
    // 创建三个点的动画
    _dotAnimations = _dotControllers.map((controller) {
      return Tween<double>(
        begin: 0.3,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ));
    }).toList();
    
    _startAnimation();
  }

  @override
  void dispose() {
    _controller.dispose();
    for (final controller in _dotControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _startAnimation() {
    _controller.repeat();
    
    // 错开启动每个点的动画
    for (int i = 0; i < _dotControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 200), () {
        if (mounted) {
          _dotControllers[i].repeat(reverse: true);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(
          bottom: AppDimensions.messageSpacing, 
          right: 60,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 助手头部信息
            _buildAgentHeader(theme),
            
            // 输入指示器气泡
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.radiusXL, 
                vertical: AppDimensions.paddingL,
              ),
              decoration: BoxDecoration(
                color: isDark 
                    ? Colors.white.withOpacity(0.05)
                    : const Color(0xFFF3F4F6),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(AppDimensions.radiusXL),
                  topRight: Radius.circular(AppDimensions.radiusXL),
                  bottomLeft: Radius.circular(AppDimensions.radiusS),
                  bottomRight: Radius.circular(AppDimensions.radiusXL),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ...List.generate(3, (index) {
                    return AnimatedBuilder(
                      animation: _dotAnimations[index],
                      builder: (context, child) {
                        return Container(
                          width: AppDimensions.typingIndicatorSize,
                          height: AppDimensions.typingIndicatorSize,
                          margin: EdgeInsets.only(
                            right: index < 2 ? 8 : 0,
                          ),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withOpacity(
                              _dotAnimations[index].value,
                            ),
                            shape: BoxShape.circle,
                          ),
                        );
                      },
                    );
                  }),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAgentHeader(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(left: 12, bottom: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.auto_awesome,
              size: 14,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            widget.agentName,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '正在输入...',
            style: TextStyle(
              fontSize: 12,
              color: theme.textTheme.bodyMedium?.color?.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }
}

/// 简化版输入指示器（用于底部状态栏）
class SimpleTypingIndicator extends StatefulWidget {
  final String text;
  final Color? color;

  const SimpleTypingIndicator({
    super.key,
    this.text = '正在输入',
    this.color,
  });

  @override
  State<SimpleTypingIndicator> createState() => _SimpleTypingIndicatorState();
}

class _SimpleTypingIndicatorState extends State<SimpleTypingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<String> _animation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _animation = TweenSequence<String>([
      TweenSequenceItem(
        tween: ConstantTween<String>('${widget.text}.'),
        weight: 1,
      ),
      TweenSequenceItem(
        tween: ConstantTween<String>('${widget.text}..'),
        weight: 1,
      ),
      TweenSequenceItem(
        tween: ConstantTween<String>('${widget.text}...'),
        weight: 1,
      ),
    ]).animate(_controller);
    
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Text(
          _animation.value,
          style: TextStyle(
            fontSize: 14,
            color: widget.color ?? theme.textTheme.bodyMedium?.color?.withOpacity(0.6),
            fontStyle: FontStyle.italic,
          ),
        );
      },
    );
  }
}