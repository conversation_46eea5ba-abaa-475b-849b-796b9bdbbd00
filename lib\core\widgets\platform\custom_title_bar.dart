import 'dart:io';
import 'package:flutter/material.dart';
import 'package:window_manager/window_manager.dart';
import '../../constants/dimensions.dart';
import '../../constants/colors.dart';
import 'window_drag_area.dart';

/// 自定义标题栏组件
class CustomTitleBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;

  const CustomTitleBar({
    super.key,
    this.title = '八字算命',
    this.showBackButton = false,
    this.onBackPressed,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
  });

  @override
  Size get preferredSize => const Size.fromHeight(AppDimensions.titleBarHeight);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      height: AppDimensions.titleBarHeight,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: isDark ? Border(
          bottom: BorderSide(
            color: AppColors.darkBorderColor,
            width: 0.5,
          ),
        ) : null,
        boxShadow: isDark ? null : [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 1),
            blurRadius: 3,
          ),
        ],
      ),
      child: Row(
        children: [
          // 左侧：图标和应用名称（或返回按钮或自定义leading）
          _buildLeading(context, theme),
          
          // 中间：可拖拽的空白区域
          Expanded(
            child: WindowDragArea(
              child: Container(
                height: double.infinity,
                color: Colors.transparent,
                alignment: Alignment.center,
                child: Text(
                  title,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          
          // 右侧：自定义操作按钮和窗口控制按钮
          if (actions != null) ...actions!,
          if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) 
            ..._buildWindowControls(context, theme),
        ],
      ),
    );
  }

  Widget _buildLeading(BuildContext context, ThemeData theme) {
    if (leading != null) {
      return Padding(
        padding: const EdgeInsets.only(left: AppDimensions.paddingL),
        child: leading!,
      );
    }

    if (showBackButton || (automaticallyImplyLeading && Navigator.of(context).canPop())) {
      // 返回按钮：不可拖拽
      return Padding(
        padding: const EdgeInsets.only(left: AppDimensions.paddingL),
        child: IconButton(
          icon: const Icon(Icons.arrow_back, size: AppDimensions.iconM),
          onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
          color: theme.colorScheme.onSurface,
          tooltip: '返回',
        ),
      );
    } else {
      // 图标和标题：可拖拽
      return WindowDragArea(
        child: Padding(
          padding: const EdgeInsets.only(left: AppDimensions.paddingL),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.brightness_7_outlined,
                size: AppDimensions.iconM,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: AppDimensions.paddingS),
            ],
          ),
        ),
      );
    }
  }

  List<Widget> _buildWindowControls(BuildContext context, ThemeData theme) {
    return [
      _WindowButton(
        icon: Icons.minimize,
        onPressed: () => windowManager.minimize(),
        tooltip: '最小化',
      ),
      _WindowButton(
        icon: Icons.crop_square,
        onPressed: () async {
          bool isMaximized = await windowManager.isMaximized();
          if (isMaximized) {
            windowManager.unmaximize();
          } else {
            windowManager.maximize();
          }
        },
        tooltip: '最大化/还原',
      ),
      _WindowButton(
        icon: Icons.close,
        onPressed: () => windowManager.close(),
        isCloseButton: true,
        tooltip: '关闭',
      ),
    ];
  }
}

/// 窗口控制按钮
class _WindowButton extends StatefulWidget {
  final IconData icon;
  final VoidCallback onPressed;
  final bool isCloseButton;
  final String? tooltip;

  const _WindowButton({
    required this.icon,
    required this.onPressed,
    this.isCloseButton = false,
    this.tooltip,
  });

  @override
  State<_WindowButton> createState() => _WindowButtonState();
}

class _WindowButtonState extends State<_WindowButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    Widget button = MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap: widget.onPressed,
        child: Container(
          width: 46,
          height: AppDimensions.titleBarHeight,
          decoration: BoxDecoration(
            color: _isHovered
                ? (widget.isCloseButton 
                    ? AppColors.errorColor 
                    : theme.colorScheme.onSurface.withOpacity(0.1))
                : Colors.transparent,
          ),
          child: Icon(
            widget.icon,
            size: AppDimensions.iconS,
            color: _isHovered && widget.isCloseButton
                ? Colors.white
                : theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ),
    );

    if (widget.tooltip != null) {
      return Tooltip(
        message: widget.tooltip!,
        child: button,
      );
    }

    return button;
  }
}