/// 八字选项模型
class BaziOption {
  final String id;
  final String name;
  final String description;
  final bool isDefault;
  final bool isAddNew;
  final DateTime? birthDate;
  final int birthYear;
  final int birthMonth;
  final int birthDay;
  final String birthHour;
  final String? birthTime;
  final String? birthPlace;
  final String gender;
  final Map<String, dynamic>? baziData;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  const BaziOption({
    required this.id,
    required this.name,
    this.description = '',
    this.isDefault = false,
    this.isAddNew = false,
    this.birthDate,
    required this.birthYear,
    required this.birthMonth,
    required this.birthDay,
    required this.birthHour,
    this.birthTime,
    this.birthPlace,
    required this.gender,
    this.baziData,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
  });

  /// 从JSON创建八字选项
  factory BaziOption.fromJson(Map<String, dynamic> json) {
    return BaziOption(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      isDefault: json['is_default'] as bool? ?? false,
      isAddNew: json['is_add_new'] as bool? ?? false,
      birthDate: json['birth_date'] != null 
          ? DateTime.parse(json['birth_date'] as String) 
          : null,
      birthYear: json['birth_year'] as int? ?? 1990,
      birthMonth: json['birth_month'] as int? ?? 1,
      birthDay: json['birth_day'] as int? ?? 1,
      birthHour: json['birth_hour'] as String? ?? '子时',
      birthTime: json['birth_time'] as String?,
      birthPlace: json['birth_place'] as String?,
      gender: json['gender'] as String? ?? 'male',
      baziData: json['bazi_data'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'is_default': isDefault,
      'is_add_new': isAddNew,
      'birth_date': birthDate?.toIso8601String(),
      'birth_year': birthYear,
      'birth_month': birthMonth,
      'birth_day': birthDay,
      'birth_hour': birthHour,
      'birth_time': birthTime,
      'birth_place': birthPlace,
      'gender': gender,
      'bazi_data': baziData,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_active': isActive,
    };
  }

  /// 创建副本
  BaziOption copyWith({
    String? id,
    String? name,
    String? description,
    bool? isDefault,
    bool? isAddNew,
    DateTime? birthDate,
    int? birthYear,
    int? birthMonth,
    int? birthDay,
    String? birthHour,
    String? birthTime,
    String? birthPlace,
    String? gender,
    Map<String, dynamic>? baziData,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return BaziOption(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      isDefault: isDefault ?? this.isDefault,
      isAddNew: isAddNew ?? this.isAddNew,
      birthDate: birthDate ?? this.birthDate,
      birthYear: birthYear ?? this.birthYear,
      birthMonth: birthMonth ?? this.birthMonth,
      birthDay: birthDay ?? this.birthDay,
      birthHour: birthHour ?? this.birthHour,
      birthTime: birthTime ?? this.birthTime,
      birthPlace: birthPlace ?? this.birthPlace,
      gender: gender ?? this.gender,
      baziData: baziData ?? this.baziData,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  /// 是否为完整的八字信息
  bool get isComplete {
    return !isAddNew && 
           birthDate != null && 
           birthTime != null && 
           birthPlace != null && 
           gender != null;
  }

  /// 获取出生信息摘要
  String get birthSummary {
    if (isAddNew) return '添加新八字';
    if (!isComplete) return '信息不完整';
    
    final dateStr = '${birthDate!.year}年${birthDate!.month}月${birthDate!.day}日';
    final timeStr = birthTime ?? '未知时辰';
    final placeStr = birthPlace ?? '未知地点';
    
    return '$dateStr $timeStr $placeStr';
  }

  /// 获取性别显示文本
  String get genderDisplay {
    switch (gender) {
      case 'male':
        return '男';
      case 'female':
        return '女';
      default:
        return '未知';
    }
  }

  /// 获取八字盘简要信息
  String? get baziSummary {
    if (baziData == null) return null;
    
    // 这里可以根据实际的八字数据结构来生成摘要
    // 示例：提取天干地支信息
    final yearPillar = baziData!['year_pillar'] as String?;
    final monthPillar = baziData!['month_pillar'] as String?;
    final dayPillar = baziData!['day_pillar'] as String?;
    final hourPillar = baziData!['hour_pillar'] as String?;
    
    if (yearPillar != null && monthPillar != null && dayPillar != null && hourPillar != null) {
      return '$yearPillar $monthPillar $dayPillar $hourPillar';
    }
    
    return '八字已排盘';
  }

  /// 验证八字数据是否有效
  bool validateBaziData() {
    if (isAddNew) return true;
    
    // 基本信息验证
    if (birthDate == null || birthTime == null || gender == null) {
      return false;
    }
    
    // 出生日期不能是未来
    if (birthDate!.isAfter(DateTime.now())) {
      return false;
    }
    
    // 性别验证
    if (!['male', 'female'].contains(gender)) {
      return false;
    }
    
    return true;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BaziOption &&
        other.id == id &&
        other.name == name &&
        other.isDefault == isDefault &&
        other.isAddNew == isAddNew &&
        other.birthDate == birthDate &&
        other.birthTime == birthTime &&
        other.birthPlace == birthPlace &&
        other.gender == gender &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      isDefault,
      isAddNew,
      birthDate,
      birthTime,
      birthPlace,
      gender,
      isActive,
    );
  }

  @override
  String toString() {
    return 'BaziOption(id: $id, name: $name, isDefault: $isDefault, isAddNew: $isAddNew, birthDate: $birthDate, birthTime: $birthTime, birthPlace: $birthPlace, gender: $gender, isActive: $isActive)';
  }
}

/// 预定义的八字选项
class PredefinedBaziOptions {
  static List<BaziOption> getDefaultOptions() {
    final now = DateTime.now();
    
    return [
      BaziOption(
        id: '1',
        name: '张三的八字',
        description: '1990年5月15日生，北京人',
        isDefault: true,
        birthDate: DateTime(1990, 5, 15),
        birthYear: 1990,
        birthMonth: 5,
        birthDay: 15,
        birthHour: '子时',
        birthTime: '子时',
        birthPlace: '北京市',
        gender: 'male',
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now.subtract(const Duration(days: 30)),
      ),
      BaziOption(
        id: '2',
        name: '李四的八字',
        description: '1985年8月22日生，上海人',
        birthDate: DateTime(1985, 8, 22),
        birthYear: 1985,
        birthMonth: 8,
        birthDay: 22,
        birthHour: '午时',
        birthTime: '午时',
        birthPlace: '上海市',
        gender: 'female',
        createdAt: now.subtract(const Duration(days: 20)),
        updatedAt: now.subtract(const Duration(days: 20)),
      ),
      BaziOption(
        id: '3',
        name: '王五的八字',
        description: '1992年12月3日生，广州人',
        birthDate: DateTime(1992, 12, 3),
        birthYear: 1992,
        birthMonth: 12,
        birthDay: 3,
        birthHour: '卯时',
        birthTime: '卯时',
        birthPlace: '广州市',
        gender: 'male',
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now.subtract(const Duration(days: 10)),
      ),
      BaziOption(
        id: 'add',
        name: '添加新八字',
        description: '点击创建新的八字档案',
        isAddNew: true,
        birthYear: 1990,
        birthMonth: 1,
        birthDay: 1,
        birthHour: '子时',
        gender: 'male',
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }

  /// 根据ID获取八字选项
  static BaziOption? getById(String id) {
    try {
      return getDefaultOptions().firstWhere((option) => option.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 获取活跃的八字选项
  static List<BaziOption> getActiveOptions() {
    return getDefaultOptions().where((option) => option.isActive).toList();
  }

  /// 获取默认八字选项
  static BaziOption? get defaultOption {
    try {
      return getDefaultOptions().firstWhere((option) => option.isDefault);
    } catch (e) {
      return null;
    }
  }
}