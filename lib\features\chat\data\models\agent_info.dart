import 'package:flutter/material.dart';
import '../../../../core/constants/colors.dart';

/// AI助手信息模型
class AgentInfo {
  final String id;
  final String name;
  final IconData icon;
  final Color color;
  final String description;
  final List<String> capabilities;
  final bool isActive;
  final Map<String, dynamic>? metadata;

  const AgentInfo({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
    required this.description,
    this.capabilities = const [],
    this.isActive = true,
    this.metadata,
  });

  /// 从JSON创建助手信息
  factory AgentInfo.fromJson(Map<String, dynamic> json) {
    return AgentInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      icon: _parseIconData(json['icon'] as String?),
      color: Color(json['color'] as int),
      description: json['description'] as String,
      capabilities: (json['capabilities'] as List<dynamic>?)?.cast<String>() ?? [],
      isActive: json['is_active'] as bool? ?? true,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'icon': icon.codePoint.toString(),
      'color': color.value,
      'description': description,
      'capabilities': capabilities,
      'is_active': isActive,
      'metadata': metadata,
    };
  }

  /// 创建副本
  AgentInfo copyWith({
    String? id,
    String? name,
    IconData? icon,
    Color? color,
    String? description,
    List<String>? capabilities,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return AgentInfo(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      description: description ?? this.description,
      capabilities: capabilities ?? this.capabilities,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 获取助手头像
  Widget getAvatar({double size = 40}) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(size / 6),
      ),
      child: Icon(
        icon,
        color: color,
        size: size * 0.6,
      ),
    );
  }

  /// 是否支持某项能力
  bool hasCapability(String capability) {
    return capabilities.contains(capability);
  }

  /// 获取能力描述
  String getCapabilitiesDescription() {
    if (capabilities.isEmpty) return description;
    return '${description}\n\n专长领域：${capabilities.join('、')}';
  }

  static IconData _parseIconData(String? iconString) {
    if (iconString == null) return Icons.auto_awesome;
    
    try {
      final codePoint = int.parse(iconString);
      return IconData(codePoint, fontFamily: 'MaterialIcons');
    } catch (e) {
      return Icons.auto_awesome;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AgentInfo &&
        other.id == id &&
        other.name == name &&
        other.color == color &&
        other.description == description &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return Object.hash(id, name, color, description, isActive);
  }

  @override
  String toString() {
    return 'AgentInfo(id: $id, name: $name, description: $description, capabilities: $capabilities, isActive: $isActive)';
  }
}

/// 预定义的AI助手
class PredefinedAgents {
  static const List<AgentInfo> agents = [
    AgentInfo(
      id: 'bazi_master',
      name: '八字大师',
      icon: Icons.auto_awesome,
      color: AppColors.agentBaziMaster,
      description: '精通八字命理，为您解析人生运势',
      capabilities: ['八字排盘', '运势分析', '命理解读', '吉凶预测'],
    ),
    AgentInfo(
      id: 'career',
      name: '事业顾问',
      icon: Icons.trending_up,
      color: AppColors.agentCareer,
      description: '职场发展建议，助您事业腾飞',
      capabilities: ['职业规划', '升职加薪', '跳槽建议', '创业指导'],
    ),
    AgentInfo(
      id: 'emotion',
      name: '感情专家',
      icon: Icons.favorite,
      color: AppColors.agentEmotion,
      description: '情感困惑解答，守护您的幸福',
      capabilities: ['恋爱指导', '婚姻咨询', '情感挽回', '桃花运势'],
    ),
    AgentInfo(
      id: 'health',
      name: '健康顾问',
      icon: Icons.spa,
      color: AppColors.agentHealth,
      description: '健康养生指导，平衡身心能量',
      capabilities: ['养生建议', '健康预测', '疾病预防', '体质调理'],
    ),
  ];

  /// 根据ID获取助手
  static AgentInfo? getById(String id) {
    try {
      return agents.firstWhere((agent) => agent.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 根据名称获取助手
  static AgentInfo? getByName(String name) {
    try {
      return agents.firstWhere((agent) => agent.name == name);
    } catch (e) {
      return null;
    }
  }

  /// 获取活跃的助手列表
  static List<AgentInfo> getActiveAgents() {
    return agents.where((agent) => agent.isActive).toList();
  }

  /// 获取默认助手
  static AgentInfo get defaultAgent => agents.first;
}