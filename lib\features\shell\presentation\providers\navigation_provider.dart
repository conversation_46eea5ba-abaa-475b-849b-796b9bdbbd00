import 'package:flutter/material.dart';

/// 导航状态管理
class NavigationProvider extends ChangeNotifier {
  int _selectedIndex = 0;

  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      id: 'chat',
      label: '智能问答',
      icon: Icons.chat_outlined,
      selectedIcon: Icons.chat,
    ),
    NavigationItem(
      id: 'bazi',
      label: '八字排盘',
      icon: Icons.calendar_month_outlined,
      selectedIcon: Icons.calendar_month,
    ),
    NavigationItem(
      id: 'profile',
      label: '个人中心',
      icon: Icons.person_outline,
      selectedIcon: Icons.person,
    ),
  ];

  int get selectedIndex => _selectedIndex;
  List<NavigationItem> get navigationItems => _navigationItems;

  void selectIndex(int index) {
    if (index != _selectedIndex && index >= 0 && index < _navigationItems.length) {
      _selectedIndex = index;
      notifyListeners();
    }
  }
}

/// 导航项数据模型
class NavigationItem {
  final String id;
  final String label;
  final IconData icon;
  final IconData selectedIcon;

  const NavigationItem({
    required this.id,
    required this.label,
    required this.icon,
    required this.selectedIcon,
  });
}