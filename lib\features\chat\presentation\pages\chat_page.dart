import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/platform/custom_title_bar.dart';
import '../../../../shared/utils/responsive_utils.dart';
import '../providers/chat_provider.dart';
import '../widgets/chat_input.dart';
import '../widgets/message_list.dart';
import '../widgets/agent_selector.dart';
import '../widgets/bazi_selector.dart';
import '../widgets/quick_actions.dart';

/// 聊天页面
class ChatPage extends StatefulWidget {
  const ChatPage({super.key});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  @override
  void initState() {
    super.initState();
    
    // 初始化聊天数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ChatProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    // 小屏幕需要自己的标题栏，大屏幕由MainShell提供
    if (ResponsiveUtils.isMobile(context)) {
      return const Scaffold(
        appBar: CustomTitleBar(
          title: '八字命理 - 智能问答',
          showBackButton: false,
        ),
        body: _ChatContent(),
      );
    } else {
      return const _ChatContent();
    }
  }
}

class _ChatContent extends StatelessWidget {
  const _ChatContent();

  @override
  Widget build(BuildContext context) {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        return Column(
          children: [
            // 顶部工具栏：助手选择 + 八字选择
            _buildTopToolbar(context, chatProvider),
            
            // 消息列表区域（可展开）
            Expanded(
              child: _buildMessageArea(context, chatProvider),
            ),
            
            // 底部输入区域
            _buildInputArea(context, chatProvider),
          ],
        );
      },
    );
  }

  Widget _buildTopToolbar(BuildContext context, ChatProvider chatProvider) {
    if (ResponsiveUtils.isMobile(context)) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AgentSelector(
              selectedAgentId: chatProvider.selectedAgentId,
              agents: chatProvider.agents,
              onAgentSelected: chatProvider.selectAgent,
            ),
            const SizedBox(height: 8),
            BaziSelector(
              selectedBaziIds: chatProvider.selectedBaziIds,
              baziOptions: chatProvider.baziOptions,
              onBaziSelected: chatProvider.toggleBaziSelection,
              isMultiSelectMode: chatProvider.isMultiSelectMode,
              onToggleMultiSelectMode: chatProvider.toggleMultiSelectMode,
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: AgentSelector(
                selectedAgentId: chatProvider.selectedAgentId,
                agents: chatProvider.agents,
                onAgentSelected: chatProvider.selectAgent,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: BaziSelector(
                selectedBaziIds: chatProvider.selectedBaziIds,
                baziOptions: chatProvider.baziOptions,
                onBaziSelected: chatProvider.toggleBaziSelection,
                isMultiSelectMode: chatProvider.isMultiSelectMode,
                onToggleMultiSelectMode: chatProvider.toggleMultiSelectMode,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildMessageArea(BuildContext context, ChatProvider chatProvider) {
    if (chatProvider.currentMessages.isEmpty) {
      return _buildEmptyState(context, chatProvider);
    }

    return MessageList(
      messages: chatProvider.currentMessages,
      isTyping: chatProvider.isTyping,
      onMessageTap: (message) {
        // 处理消息点击
      },
      onMessageLongPress: (message) {
        // 处理消息长按
        _showMessageOptions(context, message);
      },
    );
  }

  Widget _buildEmptyState(BuildContext context, ChatProvider chatProvider) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: theme.colorScheme.primary.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            '开始您的命理探索之旅',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '选择您感兴趣的问题开始对话',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          // 快捷操作按钮
          QuickActions(
            quickActions: chatProvider.quickActions,
            onQuickActionTap: (action) {
              chatProvider.sendMessage(action);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInputArea(BuildContext context, ChatProvider chatProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: ChatInput(
        onSendMessage: chatProvider.sendMessage,
        isLoading: chatProvider.isLoading,
        enabled: !chatProvider.isTyping,
      ),
    );
  }

  void _showMessageOptions(BuildContext context, dynamic message) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _MessageOptionsSheet(message: message),
    );
  }
}

class _MessageOptionsSheet extends StatelessWidget {
  final dynamic message;

  const _MessageOptionsSheet({required this.message});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.copy),
            title: const Text('复制'),
            onTap: () {
              Navigator.pop(context);
              // TODO: 实现复制功能
            },
          ),
          if (!message.isUser)
            ListTile(
              leading: const Icon(Icons.thumb_up_outlined),
              title: const Text('好评'),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现评价功能
              },
            ),
          if (!message.isUser)
            ListTile(
              leading: const Icon(Icons.thumb_down_outlined),
              title: const Text('差评'),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现评价功能
              },
            ),
          ListTile(
            leading: const Icon(Icons.delete_outline),
            title: const Text('删除'),
            onTap: () {
              Navigator.pop(context);
              // TODO: 实现删除功能
            },
          ),
        ],
      ),
    );
  }
}