import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/platform/custom_title_bar.dart';
import '../../../../core/constants/dimensions.dart';
import '../providers/auth_provider.dart';
import '../widgets/auth_form.dart';

/// 忘记密码页面
class ForgotPasswordPage extends StatelessWidget {
  const ForgotPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: const CustomTitleBar(
        title: '八字命理 - 找回密码',
        showBackButton: true,
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(AppDimensions.paddingXXL, 0, AppDimensions.paddingXXL, AppDimensions.paddingXXL),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 400),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingXXL, 
                  vertical: AppDimensions.paddingXXXL,
                ),
                child: Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: <Widget>[
                        // Title
                        Text(
                          '找回密码',
                          textAlign: TextAlign.center,
                          style: theme.textTheme.displayMedium?.copyWith(fontSize: 24),
                        ),
                        const SizedBox(height: AppDimensions.paddingS),
                        // Description
                        Text(
                          '请通过手机号重设您的密码',
                          textAlign: TextAlign.center,
                          style: theme.textTheme.bodyMedium,
                        ),
                        const SizedBox(height: AppDimensions.paddingXXXL),
                        
                        // 使用认证表单组件
                        AuthFormWidget(
                          formType: AuthFormType.forgotPassword,
                          isLoading: authProvider.isLoading,
                          errorMessage: authProvider.error,
                          onSubmit: () => _handleResetPassword(context, authProvider),
                        ),
                        
                        const SizedBox(height: AppDimensions.paddingXXL),
                        // Back to Login
                        Center(
                          child: RichText(
                            text: TextSpan(
                              text: '记起密码了? ',
                              style: theme.textTheme.bodyMedium,
                              children: <TextSpan>[
                                TextSpan(
                                  text: '返回登录',
                                  style: TextStyle(
                                    color: theme.colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleResetPassword(BuildContext context, AuthProvider authProvider) async {
    final success = await authProvider.resetPassword(
      phone: '13800138000', // 示例数据
      newPassword: '123456',
      verificationCode: '123456',
    );

    if (success && context.mounted) {
      Navigator.of(context).pop(); // 返回登录页面
    }
  }
}