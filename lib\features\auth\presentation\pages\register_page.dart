import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/platform/custom_title_bar.dart';
import '../../../../core/constants/dimensions.dart';
import '../../../../shared/widgets/privacy_policy_page.dart';
import '../../../../core/utils/ui_helpers.dart';
import '../providers/auth_provider.dart';
import '../widgets/auth_form.dart';

/// 注册页面
class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: const CustomTitleBar(
        title: '八字命理 - 注册',
        showBackButton: true,
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(AppDimensions.paddingXXL, 0, AppDimensions.paddingXXL, AppDimensions.paddingXXL),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 400),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingXXL, 
                  vertical: AppDimensions.paddingXXXL,
                ),
                child: Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: <Widget>[
                        // Title
                        Text(
                          '创建您的新账户',
                          textAlign: TextAlign.center,
                          style: theme.textTheme.displayMedium?.copyWith(fontSize: 24),
                        ),
                        const SizedBox(height: AppDimensions.paddingS),
                        // Description
                        Text(
                          '仅需几步，即可加入我们',
                          textAlign: TextAlign.center,
                          style: theme.textTheme.bodyMedium,
                        ),
                        const SizedBox(height: AppDimensions.paddingXXXL),
                        
                        // 使用认证表单组件
                        AuthFormWidget(
                          formType: AuthFormType.register,
                          isLoading: authProvider.isLoading,
                          errorMessage: authProvider.error,
                          onSubmit: () => _handleRegister(context, authProvider),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleRegister(BuildContext context, AuthProvider authProvider) async {
    final success = await authProvider.register(
      username: '新用户', // 示例数据
      phone: '13800138000',
      password: '123456',
      verificationCode: '123456',
    );

    if (success && context.mounted) {
      Navigator.of(context).pop(); // 返回登录页面
    }
  }
}