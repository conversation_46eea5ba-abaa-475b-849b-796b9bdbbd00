import 'package:flutter/material.dart';
import '../../../../core/constants/dimensions.dart';
import '../../../../core/extensions/date_extensions.dart';
import '../../../../shared/utils/responsive_utils.dart';
import '../../data/models/chat_message.dart';

/// 消息气泡组件
class MessageBubble extends StatelessWidget {
  final ChatMessage message;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool showAvatar;
  final bool showTimestamp;

  const MessageBubble({
    super.key,
    required this.message,
    this.onTap,
    this.onLongPress,
    this.showAvatar = true,
    this.showTimestamp = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final isUser = message.isUser;
    final maxWidth = ResponsiveUtils.getMessageBubbleMaxWidth(context);

    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, (1 - value) * 20),
          child: Opacity(
            opacity: value,
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: AppDimensions.messageSpacing),
              child: Align(
                alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
                child: ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: maxWidth),
                  child: Column(
                    crossAxisAlignment: isUser 
                        ? CrossAxisAlignment.end 
                        : CrossAxisAlignment.start,
                    children: [
                      if (!isUser && showAvatar)
                        _buildAgentHeader(theme),
                      _buildMessageContent(theme, isDark),
                      if (showTimestamp)
                        _buildTimestamp(theme),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAgentHeader(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(left: 12, bottom: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.auto_awesome,
              size: 14,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            message.agentName,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageContent(ThemeData theme, bool isDark) {
    final isUser = message.isUser;
    
    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Material(
        color: _getBackgroundColor(theme, isDark, isUser),
        borderRadius: _getBorderRadius(isUser),
        elevation: 0,
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingL,
            vertical: AppDimensions.paddingM,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildMessageText(theme, isUser),
              if (message.status == MessageStatus.failed)
                _buildErrorIndicator(theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMessageText(ThemeData theme, bool isUser) {
    return Text(
      message.content,
      style: TextStyle(
        fontSize: 14,
        color: isUser 
            ? Colors.white 
            : theme.textTheme.bodyLarge?.color,
        height: 1.4,
      ),
    );
  }

  Widget _buildErrorIndicator(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            size: 16,
            color: message.isUser ? Colors.white70 : Colors.red,
          ),
          const SizedBox(width: 4),
          Text(
            '发送失败',
            style: TextStyle(
              fontSize: 12,
              color: message.isUser ? Colors.white70 : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimestamp(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(
        top: 4, 
        left: 12, 
        right: 12,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            message.timestamp.toTimeString(),
            style: TextStyle(
              fontSize: 11,
              color: theme.textTheme.bodyMedium?.color?.withOpacity(0.5),
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: 4),
            _buildStatusIndicator(theme),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusIndicator(ThemeData theme) {
    IconData icon;
    Color color;
    
    switch (message.status) {
      case MessageStatus.sending:
        icon = Icons.schedule;
        color = Colors.grey;
        break;
      case MessageStatus.sent:
        icon = Icons.check;
        color = Colors.grey;
        break;
      case MessageStatus.delivered:
        icon = Icons.done_all;
        color = theme.colorScheme.primary;
        break;
      case MessageStatus.failed:
        icon = Icons.error_outline;
        color = Colors.red;
        break;
    }
    
    return Icon(
      icon,
      size: 12,
      color: color,
    );
  }

  Color _getBackgroundColor(ThemeData theme, bool isDark, bool isUser) {
    if (isUser) {
      return theme.colorScheme.primary;
    } else {
      return isDark 
          ? Colors.white.withOpacity(0.05)
          : const Color(0xFFF3F4F6);
    }
  }

  BorderRadius _getBorderRadius(bool isUser) {
    return BorderRadius.only(
      topLeft: const Radius.circular(AppDimensions.radiusXL),
      topRight: const Radius.circular(AppDimensions.radiusXL),
      bottomLeft: Radius.circular(isUser ? AppDimensions.radiusXL : AppDimensions.radiusS),
      bottomRight: Radius.circular(isUser ? AppDimensions.radiusS : AppDimensions.radiusXL),
    );
  }
}