/// 尺寸常量定义
class AppDimensions {
  AppDimensions._();

  // 边距和内边距
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 12.0;
  static const double paddingL = 16.0;
  static const double paddingXL = 20.0;
  static const double paddingXXL = 24.0;
  static const double paddingXXXL = 32.0;

  // 圆角半径
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  static const double radiusXXL = 20.0;
  static const double radiusCircle = 50.0;

  // 组件高度
  static const double buttonHeight = 44.0;
  static const double inputHeight = 44.0;
  static const double titleBarHeight = 50.0;
  static const double bottomNavHeight = 60.0;
  static const double cardElevation = 2.0;

  // 导航宽度
  static const double compactNavWidth = 64.0;
  static const double standardNavWidth = 200.0;
  static const double extendedNavWidth = 280.0;
  static const double sidebarMaxWidth = 320.0;

  // 阴影
  static const double shadowBlurRadius = 8.0;
  static const double shadowOffset = 2.0;
  
  // 聊天相关
  static const double messageBubbleMaxWidth = 500.0;
  static const double messageSpacing = 12.0;
  static const double chatInputMaxLines = 5.0;
  static const double typingIndicatorSize = 8.0;

  // 图标尺寸
  static const double iconS = 16.0;
  static const double iconM = 20.0;
  static const double iconL = 24.0;
  static const double iconXL = 32.0;
  static const double iconXXL = 48.0;
  static const double iconXXXL = 60.0;
}