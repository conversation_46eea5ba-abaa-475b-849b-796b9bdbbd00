/// 应用常量定义
class AppConstants {
  AppConstants._();

  // 应用信息
  static const String appName = '八字命理';
  static const String appVersion = '1.0.0';
  
  // 窗口配置
  static const double defaultWindowWidth = 450;
  static const double defaultWindowHeight = 750;
  static const double minWindowWidth = 400;
  static const double minWindowHeight = 700;
  
  // 屏幕断点
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;
  
  // 动画时长
  static const int defaultAnimationDuration = 300;
  static const int longAnimationDuration = 600;
  static const int shortAnimationDuration = 150;
  
  // AI聊天配置
  static const int maxChatHistoryLength = 100;
  static const int typingIndicatorDelay = 800;
  static const int maxMessageLength = 1000;
  
  // 验证配置
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 20;
  static const int verificationCodeLength = 6;
  
  // 缓存配置
  static const String userPrefsKey = 'user_preferences';
  static const String themePrefsKey = 'theme_preferences';
  static const String chatHistoryKey = 'chat_history';
}