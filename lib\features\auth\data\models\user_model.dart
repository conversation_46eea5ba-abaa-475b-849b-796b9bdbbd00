/// 用户模型
class UserModel {
  final String id;
  final String username;
  final String phone;
  final String? email;
  final String? avatar;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final Map<String, dynamic>? metadata;

  const UserModel({
    required this.id,
    required this.username,
    required this.phone,
    this.email,
    this.avatar,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.metadata,
  });

  /// 从JSON创建用户模型
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      username: json['username'] as String,
      phone: json['phone'] as String,
      email: json['email'] as String?,
      avatar: json['avatar'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isActive: json['is_active'] as bool? ?? true,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'phone': phone,
      'email': email,
      'avatar': avatar,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_active': isActive,
      'metadata': metadata,
    };
  }

  /// 创建副本
  UserModel copyWith({
    String? id,
    String? username,
    String? phone,
    String? email,
    String? avatar,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      avatar: avatar ?? this.avatar,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 获取显示名称
  String get displayName {
    if (username.isNotEmpty) return username;
    if (phone.isNotEmpty) return phone;
    return '用户${id.substring(0, 8)}';
  }

  /// 获取头像URL或默认头像
  String get avatarUrl {
    if (avatar != null && avatar!.isNotEmpty) {
      return avatar!;
    }
    // 返回默认头像或生成头像
    return 'https://ui-avatars.com/api/?name=${Uri.encodeComponent(displayName)}&background=3B8BFA&color=fff';
  }

  /// 判断是否有邮箱
  bool get hasEmail => email != null && email!.isNotEmpty;

  /// 判断用户信息是否完整
  bool get isProfileComplete {
    return username.isNotEmpty && phone.isNotEmpty;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel &&
        other.id == id &&
        other.username == username &&
        other.phone == phone &&
        other.email == email &&
        other.avatar == avatar &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      username,
      phone,
      email,
      avatar,
      createdAt,
      updatedAt,
      isActive,
    );
  }

  @override
  String toString() {
    return 'UserModel(id: $id, username: $username, phone: $phone, email: $email, avatar: $avatar, createdAt: $createdAt, updatedAt: $updatedAt, isActive: $isActive, metadata: $metadata)';
  }
}