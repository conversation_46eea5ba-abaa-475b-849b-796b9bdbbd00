import 'chat_message.dart';

/// 聊天对话模型
class ChatConversation {
  final String id;
  final String title;
  final List<ChatMessage> messages;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? agentId;
  final List<String>? baziIds;
  final bool isPinned;
  final Map<String, dynamic>? metadata;

  const ChatConversation({
    required this.id,
    required this.title,
    required this.messages,
    required this.createdAt,
    required this.updatedAt,
    this.agentId,
    this.baziIds,
    this.isPinned = false,
    this.metadata,
  });

  /// 从JSON创建对话
  factory ChatConversation.fromJson(Map<String, dynamic> json) {
    return ChatConversation(
      id: json['id'] as String,
      title: json['title'] as String,
      messages: (json['messages'] as List<dynamic>?)
              ?.map((m) => ChatMessage.fromJson(m as Map<String, dynamic>))
              .toList() ??
          [],
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      agentId: json['agent_id'] as String?,
      baziIds: (json['bazi_ids'] as List<dynamic>?)?.cast<String>(),
      isPinned: json['is_pinned'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'messages': messages.map((m) => m.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'agent_id': agentId,
      'bazi_ids': baziIds,
      'is_pinned': isPinned,
      'metadata': metadata,
    };
  }

  /// 创建副本
  ChatConversation copyWith({
    String? id,
    String? title,
    List<ChatMessage>? messages,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? agentId,
    List<String>? baziIds,
    bool? isPinned,
    Map<String, dynamic>? metadata,
  }) {
    return ChatConversation(
      id: id ?? this.id,
      title: title ?? this.title,
      messages: messages ?? this.messages,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      agentId: agentId ?? this.agentId,
      baziIds: baziIds ?? this.baziIds,
      isPinned: isPinned ?? this.isPinned,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 添加消息
  ChatConversation addMessage(ChatMessage message) {
    final newMessages = List<ChatMessage>.from(messages)..add(message);
    return copyWith(
      messages: newMessages,
      updatedAt: DateTime.now(),
    );
  }

  /// 更新消息
  ChatConversation updateMessage(String messageId, ChatMessage updatedMessage) {
    final newMessages = messages.map((message) {
      return message.id == messageId ? updatedMessage : message;
    }).toList();
    
    return copyWith(
      messages: newMessages,
      updatedAt: DateTime.now(),
    );
  }

  /// 删除消息
  ChatConversation removeMessage(String messageId) {
    final newMessages = messages.where((message) => message.id != messageId).toList();
    return copyWith(
      messages: newMessages,
      updatedAt: DateTime.now(),
    );
  }

  /// 获取最后一条消息
  ChatMessage? get lastMessage {
    return messages.isNotEmpty ? messages.last : null;
  }

  /// 获取消息数量
  int get messageCount => messages.length;

  /// 获取用户消息数量
  int get userMessageCount => messages.where((m) => m.isUser).length;

  /// 获取AI消息数量
  int get aiMessageCount => messages.where((m) => !m.isUser).length;

  /// 是否为空对话
  bool get isEmpty => messages.isEmpty;

  /// 是否有未读消息（这里简单实现，实际可能需要更复杂的逻辑）
  bool get hasUnreadMessages {
    // 简单实现：检查最后一条消息是否为AI消息
    final last = lastMessage;
    return last != null && !last.isUser;
  }

  /// 生成对话标题（基于第一条用户消息）
  String generateTitle() {
    final firstUserMessage = messages.firstWhere(
      (m) => m.isUser,
      orElse: () => ChatMessage.user(content: '新对话', agentName: ''),
    );
    
    String content = firstUserMessage.content.trim();
    if (content.isEmpty) return '新对话';
    
    // 限制标题长度
    if (content.length > 20) {
      content = '${content.substring(0, 20)}...';
    }
    
    return content;
  }

  /// 获取对话摘要
  String getSummary() {
    if (isEmpty) return '暂无消息';
    
    final last = lastMessage!;
    String content = last.content.trim();
    
    if (content.length > 50) {
      content = '${content.substring(0, 50)}...';
    }
    
    return last.isUser ? '你: $content' : '${last.agentName}: $content';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatConversation &&
        other.id == id &&
        other.title == title &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.agentId == agentId &&
        other.isPinned == isPinned;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      title,
      createdAt,
      updatedAt,
      agentId,
      isPinned,
    );
  }

  @override
  String toString() {
    return 'ChatConversation(id: $id, title: $title, messageCount: $messageCount, createdAt: $createdAt, updatedAt: $updatedAt, agentId: $agentId, baziIds: $baziIds, isPinned: $isPinned)';
  }
}